#!/usr/bin/env ts-node

/**
 * Tournament Test Data Cleanup Script
 * 
 * Cleans up all generated test data including:
 * - Test users (usernames starting with 'testuser')
 * - Test tournaments (titles containing 'Test' or created by test users)
 * - Test submissions (from test tournaments)
 * - Test votes (from test tournaments)
 * - Test bracket info (matchups from test tournaments)
 * - Test coin transactions (from test users)
 * - Test winners (from test tournaments)
 */

import { PrismaClient } from '@prisma/client'
import * as dotenv from 'dotenv'

/**
 * Dynamic Environment Detection for Test Scripts
 * Automatically detects and connects to the appropriate database environment
 */
function detectEnvironment() {
  // Check command line argument for environment override
  const envArg = process.argv.find(arg => arg.startsWith('--env='))
  if (envArg) {
    const env = envArg.split('=')[1]
    console.log(`🎯 Using environment override: ${env}`)
    return env
  }

  // Auto-detect based on running Docker containers
  const { execSync } = require('child_process')

  try {
    // Check if development Docker containers are running
    // Using safer execSync with explicit shell: false and array arguments
    const devContainers = execSync('docker ps --format "{{.Names}}"', {
      encoding: 'utf8',
      timeout: 5000 // 5 second timeout
    }).trim()

    if (devContainers && (devContainers.includes('getrankt-api-dev') || devContainers.includes('getrankt-db-dev'))) {
      const devLines = devContainers.split('\n').filter((line: string) =>
        line.includes('getrankt') && line.includes('dev')
      )
      if (devLines.length > 0) {
        console.log(`🔍 Detected development Docker containers: ${devLines.join(', ')}`)
        return 'dev'
      }
    }

    // Check if local production Docker containers are running
    if (devContainers && (devContainers.includes('getrankt-api-local') || devContainers.includes('getrankt-db-local'))) {
      const localLines = devContainers.split('\n').filter((line: string) =>
        line.includes('getrankt') && line.includes('local')
      )
      if (localLines.length > 0) {
        console.log(`🔍 Detected local production Docker containers: ${localLines.join(', ')}`)
        return 'local'
      }
    }

    // Default to local if no containers detected
    console.log(`🔍 No Docker containers detected, defaulting to local production`)
    return 'local'
  } catch (error) {
    console.log(`⚠️  Could not detect environment, defaulting to local production`)
    return 'local'
  }
}

// Detect environment and load appropriate configuration
const environment = detectEnvironment()
let envFile: string
let dbConfig: any

if (environment === 'dev') {
  // Development Docker environment
  envFile = '.env'
  dotenv.config({ path: envFile })
  dbConfig = {
    user: process.env.DB_USER || 'admin',
    password: process.env.DB_PASSWORD || 'admin',
    host: 'localhost',
    port: process.env.DB_EXTERNAL_PORT, // Development Docker port
    name: 'getrankt_dev', // Always use dev database for dev environment
    schema: process.env.DB_SCHEMA || 'public'
  }
} else {
  // Local production Docker environment
  envFile = '.env.local'
  dotenv.config({ path: envFile })
  dbConfig = {
    user: process.env.DB_USER || 'admin',
    password: process.env.DB_PASSWORD || 'admin',
    host: 'localhost',
    port: process.env.DB_EXTERNAL_PORT, // Local production Docker port
    name: 'getrankt', // Always use production database for local environment
    schema: process.env.DB_SCHEMA || 'public'
  }
}

const DATABASE_URL = `postgresql://${dbConfig.user}:${dbConfig.password}@${dbConfig.host}:${dbConfig.port}/${dbConfig.name}?schema=${dbConfig.schema}`

console.log(`📊 Environment: ${environment}`)
console.log(`📁 Config file: ${envFile}`)
console.log(`🗄️  Database: ${dbConfig.name} on port ${dbConfig.port}`)
// Configure Prisma to connect to database
const prisma = new PrismaClient({
  datasources: {
    db: {
      url: DATABASE_URL
    }
  }
})

interface CleanupStats {
  users: number
  tournaments: number
  submissions: number
  votes: number
  matchups: number
  coinTransactions: number
}

class TournamentTestDataCleanup {
  private stats: CleanupStats = {
    users: 0,
    tournaments: 0,
    submissions: 0,
    votes: 0,
    matchups: 0,
    coinTransactions: 0
  }

  async cleanupTestData() {
    console.log('🧹 Starting tournament test data cleanup...')
    console.log(`📊 Connecting to database: ${DATABASE_URL.replace(/:[^:@]*@/, ':***@')}`)

    try {
      // Step 1: Find test users
      const testUsers = await this.findTestUsers()
      console.log(`Found ${testUsers.length} test users to clean up`)
      
      // Step 2: Find test tournaments (created by test users or with test titles)
      const testTournaments = await this.findTestTournaments(testUsers)
      console.log(`Found ${testTournaments.length} test tournaments to clean up`)
      
      // Step 3: Clean up in proper order (respecting foreign key constraints)
      await this.cleanupVotes(testTournaments)
      await this.cleanupMatchups(testTournaments)
      await this.cleanupSubmissions(testTournaments)
      await this.cleanupCoinTransactions(testUsers, testTournaments)
      await this.cleanupTournaments(testTournaments)
      await this.cleanupUsers(testUsers)
      
      console.log('✅ Tournament test data cleanup completed successfully!')
      this.printCleanupStats()
      
    } catch (error) {
      console.error('❌ Error during cleanup:', error)
      throw error
    }
  }

  /**
   * Find all test users (usernames starting with 'testuser')
   */
  private async findTestUsers() {
    return await prisma.user.findMany({
      where: {
        OR: [
          { username: { startsWith: 'testuser' } },
          { email: { contains: '@test.com' } }
        ]
      },
      select: { id: true, username: true, email: true }
    })
  }

  /**
   * Find all test tournaments
   */
  private async findTestTournaments(testUsers: any[]) {
    const testUserIds = testUsers.map(u => u.id)
    
    return await prisma.tournament.findMany({
      where: {
        OR: [
          { createdById: { in: testUserIds } },
          { title: { contains: 'Test' } },
          { title: { contains: 'Mini Tournament' } },
          { title: { contains: 'Small Tournament' } },
          { title: { contains: 'Medium Tournament' } },
          { title: { contains: 'Large Tournament' } }
        ]
      },
      select: { id: true, title: true, createdById: true }
    })
  }

  /**
   * Clean up votes from test tournaments
   */
  private async cleanupVotes(testTournaments: any[]) {
    console.log('🗳️  Cleaning up test votes...')
    
    const testTournamentIds = testTournaments.map(t => t.id)
    
    if (testTournamentIds.length > 0) {
      const result = await prisma.vote.deleteMany({
        where: {
          tournamentId: { in: testTournamentIds }
        }
      })
      
      this.stats.votes = result.count
      console.log(`✅ Deleted ${result.count} test votes`)
    }
  }

  /**
   * Clean up matchups from test tournaments
   */
  private async cleanupMatchups(testTournaments: any[]) {
    console.log('🥊 Cleaning up test matchups...')
    
    const testTournamentIds = testTournaments.map(t => t.id)
    
    if (testTournamentIds.length > 0) {
      const result = await prisma.matchup.deleteMany({
        where: {
          tournamentId: { in: testTournamentIds }
        }
      })
      
      this.stats.matchups = result.count
      console.log(`✅ Deleted ${result.count} test matchups`)
    }
  }

  /**
   * Clean up submissions from test tournaments
   */
  private async cleanupSubmissions(testTournaments: any[]) {
    console.log('📝 Cleaning up test submissions...')
    
    const testTournamentIds = testTournaments.map(t => t.id)
    
    if (testTournamentIds.length > 0) {
      const result = await prisma.submission.deleteMany({
        where: {
          tournamentId: { in: testTournamentIds }
        }
      })
      
      this.stats.submissions = result.count
      console.log(`✅ Deleted ${result.count} test submissions`)
    }
  }

  /**
   * Clean up coin transactions from test users and tournaments
   */
  private async cleanupCoinTransactions(testUsers: any[], testTournaments: any[]) {
    console.log('💰 Cleaning up test coin transactions...')
    
    const testUserIds = testUsers.map(u => u.id)
    const testTournamentIds = testTournaments.map(t => t.id)
    
    if (testUserIds.length > 0 || testTournamentIds.length > 0) {
      const result = await prisma.coinTransaction.deleteMany({
        where: {
          OR: [
            { userId: { in: testUserIds } },
            { relatedTournamentId: { in: testTournamentIds } }
          ]
        }
      })
      
      this.stats.coinTransactions = result.count
      console.log(`✅ Deleted ${result.count} test coin transactions`)
    }
  }

  /**
   * Clean up test tournaments
   */
  private async cleanupTournaments(testTournaments: any[]) {
    console.log('🏆 Cleaning up test tournaments...')
    
    const testTournamentIds = testTournaments.map(t => t.id)
    
    if (testTournamentIds.length > 0) {
      const result = await prisma.tournament.deleteMany({
        where: {
          id: { in: testTournamentIds }
        }
      })
      
      this.stats.tournaments = result.count
      console.log(`✅ Deleted ${result.count} test tournaments`)
    }
  }

  /**
   * Clean up test users
   */
  private async cleanupUsers(testUsers: any[]) {
    console.log('👥 Cleaning up test users...')
    
    const testUserIds = testUsers.map(u => u.id)
    
    if (testUserIds.length > 0) {
      const result = await prisma.user.deleteMany({
        where: {
          id: { in: testUserIds }
        }
      })
      
      this.stats.users = result.count
      console.log(`✅ Deleted ${result.count} test users`)
    }
  }

  /**
   * Print cleanup statistics
   */
  private printCleanupStats() {
    console.log('\n📊 Cleanup Statistics:')
    console.log('========================')
    console.log(`👥 Users deleted: ${this.stats.users}`)
    console.log(`🏆 Tournaments deleted: ${this.stats.tournaments}`)
    console.log(`📝 Submissions deleted: ${this.stats.submissions}`)
    console.log(`🗳️  Votes deleted: ${this.stats.votes}`)
    console.log(`🥊 Matchups deleted: ${this.stats.matchups}`)
    console.log(`💰 Coin transactions deleted: ${this.stats.coinTransactions}`)
    console.log('========================')
    
    const total = Object.values(this.stats).reduce((sum, count) => sum + count, 0)
    console.log(`🎯 Total records deleted: ${total}`)
  }

  /**
   * Verify cleanup completion
   */
  async verifyCleanup() {
    console.log('\n🔍 Verifying cleanup completion...')
    
    // Check for remaining test users
    const remainingUsers = await prisma.user.count({
      where: {
        OR: [
          { username: { startsWith: 'testuser' } },
          { email: { contains: '@test.com' } }
        ]
      }
    })
    
    // Check for remaining test tournaments
    const remainingTournaments = await prisma.tournament.count({
      where: {
        OR: [
          { title: { contains: 'Test' } },
          { title: { contains: 'Mini Tournament' } },
          { title: { contains: 'Small Tournament' } },
          { title: { contains: 'Medium Tournament' } },
          { title: { contains: 'Large Tournament' } }
        ]
      }
    })
    
    if (remainingUsers === 0 && remainingTournaments === 0) {
      console.log('✅ Cleanup verification passed - no test data remaining')
    } else {
      console.log(`⚠️  Cleanup verification found remaining data:`)
      console.log(`   - Test users: ${remainingUsers}`)
      console.log(`   - Test tournaments: ${remainingTournaments}`)
    }
    
    return { remainingUsers, remainingTournaments }
  }

  /**
   * Interactive cleanup with confirmation
   */
  async interactiveCleanup() {
    // Find test data first
    const testUsers = await this.findTestUsers()
    const testTournaments = await this.findTestTournaments(testUsers)
    
    console.log('\n🔍 Test Data Summary:')
    console.log('=====================')
    console.log(`👥 Test users found: ${testUsers.length}`)
    console.log(`🏆 Test tournaments found: ${testTournaments.length}`)
    
    if (testUsers.length === 0 && testTournaments.length === 0) {
      console.log('✅ No test data found to clean up')
      return
    }
    
    // Show sample data
    if (testUsers.length > 0) {
      console.log('\nSample test users:')
      testUsers.slice(0, 5).forEach(u => console.log(`  - ${u.username} (${u.email})`))
      if (testUsers.length > 5) console.log(`  ... and ${testUsers.length - 5} more`)
    }
    
    if (testTournaments.length > 0) {
      console.log('\nSample test tournaments:')
      testTournaments.slice(0, 5).forEach(t => console.log(`  - ${t.title}`))
      if (testTournaments.length > 5) console.log(`  ... and ${testTournaments.length - 5} more`)
    }
    
    // In a real interactive script, you would prompt for confirmation here
    // For now, we'll proceed with cleanup
    console.log('\n⚠️  Proceeding with cleanup...')
    await this.cleanupTestData()
    await this.verifyCleanup()
  }
}

// Main execution
async function main() {
  const cleanup = new TournamentTestDataCleanup()
  
  // Check command line arguments
  const args = process.argv.slice(2)
  const isInteractive = args.includes('--interactive') || args.includes('-i')
  const isVerifyOnly = args.includes('--verify-only') || args.includes('-v')
  
  if (isVerifyOnly) {
    await cleanup.verifyCleanup()
  } else if (isInteractive) {
    await cleanup.interactiveCleanup()
  } else {
    await cleanup.cleanupTestData()
    await cleanup.verifyCleanup()
  }
  
  await prisma.$disconnect()
}

if (require.main === module) {
  main().catch((error) => {
    console.error('❌ Cleanup script failed:', error)
    process.exit(1)
  })
}

export { TournamentTestDataCleanup }
