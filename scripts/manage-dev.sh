#!/bin/bash

# Development Docker Management Script
# Provides various management commands for development Docker deployment

set -e

COMPOSE_FILE="docker-compose.dev.yml"
ENV_FILE=".env.dev"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if .env.dev exists
check_env_file() {
    if [ ! -f "$ENV_FILE" ]; then
        print_error "Environment file $ENV_FILE not found!"
        print_status "Creating $ENV_FILE from template..."
        cp .env.dev.example .env.dev 2>/dev/null || {
            print_error "Template file .env.dev.example not found!"
            print_status "Please create $ENV_FILE manually or copy from .env.dev.example"
            exit 1
        }
        print_success "Created $ENV_FILE from template"
    fi
}

# Function to start services
start_services() {
    print_status "Starting development environment..."
    check_env_file

    docker-compose -f $COMPOSE_FILE --env-file $ENV_FILE up -d

    print_status "Waiting for database to be ready..."
    sleep 5

    print_status "Setting up database schema..."
    if docker-compose -f $COMPOSE_FILE exec -T api-server npx prisma db push --accept-data-loss > /dev/null 2>&1; then
        print_success "Database schema created successfully!"
    else
        print_warning "Database schema setup failed, but continuing..."
        print_status "You can manually run: bash scripts/manage-dev.sh db-push"
    fi

    print_success "Development environment started!"
    print_status "Services available at:"
    echo "  🌐 API Server: http://localhost:4000"
    echo "  🗄️  Database: localhost:5431"
    echo "  🔴 Redis: localhost:6380"
    echo "  📊 Prisma Studio: http://localhost:5556 (run with --profile tools)"
}

# Function to stop services
stop_services() {
    print_status "Stopping development environment..."
    # Stop any running Prisma Studio processes in the API container
    if docker ps --format "{{.Names}}" | grep -q "getrankt-api-dev"; then
        print_status "Stopping any running Prisma Studio processes..."
        docker-compose -f $COMPOSE_FILE exec -T api-server pkill -f "prisma studio" 2>/dev/null || true
    fi
    docker-compose -f $COMPOSE_FILE down
    print_success "Development environment stopped!"
}

# Function to restart services
restart_services() {
    print_status "Restarting development environment..."
    stop_services
    start_services
}

# Function to show logs
show_logs() {
    local service=$1
    if [ -z "$service" ]; then
        docker-compose -f $COMPOSE_FILE logs -f
    else
        docker-compose -f $COMPOSE_FILE logs -f "$service"
    fi
}

# Function to show status
show_status() {
    print_status "Development environment status:"
    docker-compose -f $COMPOSE_FILE ps
}

# Function to run database migrations
run_migrations() {
    print_status "Running database migrations..."
    docker-compose -f $COMPOSE_FILE exec api-server npx prisma migrate dev
    print_success "Database migrations completed!"
}

# Function to push database schema
push_database_schema() {
    print_status "Pushing database schema..."
    docker-compose -f $COMPOSE_FILE exec api-server npx prisma db push --accept-data-loss
    print_success "Database schema pushed successfully!"
}

# Function to seed database
seed_database() {
    print_status "Seeding database..."
    docker-compose -f $COMPOSE_FILE exec api-server npm run test:create-tournaments
    print_success "Database seeded!"
}

# Function to start Prisma Studio
start_studio() {
    print_status "Starting Prisma Studio..."
    docker-compose -f $COMPOSE_FILE exec -d api-server npx prisma studio --hostname 0.0.0.0 --port 5555
    print_success "Prisma Studio started at http://localhost:5556"
    print_status "Note: Studio runs inside the API container and may take a moment to start"
}

# Function to stop Prisma Studio
stop_studio() {
    print_status "Stopping Prisma Studio..."
    if docker ps --format "{{.Names}}" | grep -q "getrankt-api-dev"; then
        docker-compose -f $COMPOSE_FILE exec -T api-server pkill -f "prisma studio" 2>/dev/null || true
        print_success "Prisma Studio stopped"
    else
        print_error "Development environment is not running"
        exit 1
    fi
}

# Function to clean up
cleanup() {
    print_status "Cleaning up development environment..."
    docker-compose -f $COMPOSE_FILE down -v --remove-orphans
    docker system prune -f
    print_success "Cleanup completed!"
}

# Function to build images
build_images() {
    print_status "Building development images..."
    docker-compose -f $COMPOSE_FILE build --no-cache
    print_success "Images built successfully!"
}

# Function to show help
show_help() {
    echo "Development Docker Management Script"
    echo ""
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  start       Start the development environment"
    echo "  stop        Stop the development environment"
    echo "  restart     Restart the development environment"
    echo "  status      Show status of all services"
    echo "  logs        Show logs from all services"
    echo "  logs-api    Show logs from API server only"
    echo "  logs-consumer Show logs from vote consumer only"
    echo "  logs-db     Show logs from database only"
    echo "  migrate     Run database migrations"
    echo "  db-push     Push database schema (creates tables)"
    echo "  seed        Seed database with test data"
    echo "  studio      Start Prisma Studio"
    echo "  studio-stop Stop Prisma Studio"
    echo "  build       Build Docker images"
    echo "  cleanup     Stop services and remove volumes"
    echo "  help        Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 start"
    echo "  $0 logs-api"
    echo "  $0 migrate"
}

# Main script logic
case "${1:-help}" in
    start)
        start_services
        ;;
    stop)
        stop_services
        ;;
    restart)
        restart_services
        ;;
    status)
        show_status
        ;;
    logs)
        show_logs
        ;;
    logs-api)
        show_logs api-server
        ;;
    logs-consumer)
        show_logs vote-consumer
        ;;
    logs-db)
        show_logs db
        ;;
    migrate)
        run_migrations
        ;;
    db-push)
        push_database_schema
        ;;
    seed)
        seed_database
        ;;
    studio)
        start_studio
        ;;
    studio-stop)
        stop_studio
        ;;
    build)
        build_images
        ;;
    cleanup)
        cleanup
        ;;
    help|--help|-h)
        show_help
        ;;
    *)
        print_error "Unknown command: $1"
        show_help
        exit 1
        ;;
esac
