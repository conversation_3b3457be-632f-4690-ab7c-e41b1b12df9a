#!/usr/bin/env ts-node

import { redisManager } from '../src/redis/RedisConfig'
import { VoteProducer } from '../src/redis/VoteProducer'
import readline from 'readline'
import { prisma } from '../src/providers/PrismaProvider'

interface TestData {
  tournament: any
  contestants: Array<{
    user: any
    submission: any
  }>
  voters: any[]
}

let testData: TestData | null = null

async function initializeProducer() {
  console.log('🚀 Redis Vote Producer Starting...')
  console.log('=====================================')
  
  try {
    // Initialize Redis connection
    console.log('🔄 Initializing Redis connection...')
    await redisManager.initializeVotingStream()
    console.log('✅ Redis connection initialized')

    // Create or get test data
    console.log('🔄 Setting up test data...')
    testData = await createTestData()
    console.log('✅ Test data ready')

    console.log('\n🎯 Producer is ready to send votes!')
    console.log('📝 Available commands:')
    console.log('   vote <count>     - Send <count> random votes (default: 1)')
    console.log('   burst <count>    - Send <count> votes rapidly')
    console.log('   status          - Show queue status')
    console.log('   info            - Show test data info')
    console.log('   help            - Show this help')
    console.log('   exit            - Exit the producer')
    console.log('\n💡 Example: vote 10')

  } catch (error) {
    console.error('💥 Failed to initialize producer:', error)
    process.exit(1)
  }
}

async function createTestData(): Promise<TestData> {
  // Create test user
  const testUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      username: 'producer-testuser',
      passwordHash: 'test',
      coins: 1000
    }
  })

  // Create test tournament
  let tournament = await prisma.tournament.findFirst({
    where: { title: 'Redis Producer Test Tournament' }
  })

  if (!tournament) {
    tournament = await prisma.tournament.create({
      data: {
        title: 'Redis Producer Test Tournament',
        category: 'test',
        createdById: testUser.id,
        entryFee: 0,
        status: 'active',
        maxContestants: 64
      }
    })
  }

  // Create contestants if they don't exist
  const contestants: Array<{ user: any; submission: any }> = []
  for (let i = 1; i <= 8; i++) {
    const contestant = await prisma.user.upsert({
      where: { email: `producer-contestant${i}@example.com` },
      update: {},
      create: {
        email: `producer-contestant${i}@example.com`,
        username: `producer-contestant${i}`,
        passwordHash: 'test',
        coins: 100
      }
    })

    let submission = await prisma.submission.findFirst({
      where: {
        userId: contestant.id,
        tournamentId: tournament.id
      }
    })

    if (!submission) {
      submission = await prisma.submission.create({
        data: {
          userId: contestant.id,
          tournamentId: tournament.id,
          videoUrl: `https://example.com/producer-video${i}.mp4`,
          status: 'active'
        }
      })
    }

    contestants.push({ user: contestant, submission })
  }

  // Create voters if they don't exist
  const voters: any[] = []
  for (let i = 1; i <= 12; i++) {
    const voter = await prisma.user.upsert({
      where: { email: `producer-voter${i}@example.com` },
      update: {},
      create: {
        email: `producer-voter${i}@example.com`,
        username: `producer-voter${i}`,
        passwordHash: 'test',
        coins: 50
      }
    })
    voters.push(voter)
  }

  console.log(`✅ Test data: ${contestants.length} contestants, ${voters.length} voters`)
  
  return { tournament, contestants, voters }
}

function generateRandomVote() {
  if (!testData) {
    throw new Error('Test data not initialized')
  }

  const { tournament, contestants, voters } = testData

  // Pick random voter
  const voter = voters[Math.floor(Math.random() * voters.length)]

  // Pick two different random contestants
  const firstIndex = Math.floor(Math.random() * contestants.length)
  let secondIndex = Math.floor(Math.random() * contestants.length)
  
  while (secondIndex === firstIndex) {
    secondIndex = Math.floor(Math.random() * contestants.length)
  }

  const firstContestant = contestants[firstIndex]
  const secondContestant = contestants[secondIndex]
  
  // Randomly pick winner
  const winner = Math.random() < 0.5 ? firstContestant : secondContestant

  return {
    voterId: voter.id,
    tournamentId: tournament.id,
    firstSubmissionId: firstContestant.submission.id,
    secondSubmissionId: secondContestant.submission.id,
    winnerSubmissionId: winner.submission.id
  }
}

async function sendVotes(count: number, rapid: boolean = false) {
  console.log(`\n📤 Sending ${count} vote${count > 1 ? 's' : ''}...`)
  
  const startTime = Date.now()
  let successful = 0
  let failed = 0

  for (let i = 0; i < count; i++) {
    try {
      const voteRequest = generateRandomVote()
      const result = await VoteProducer.submitVoteRequest(voteRequest)
      
      if (result.success) {
        successful++
        console.log(`✅ Vote ${i + 1}/${count} queued: ${result.requestId}`)
      } else {
        failed++
        console.log(`❌ Vote ${i + 1}/${count} failed: ${result.error}`)
      }

      // Add delay unless it's a rapid burst
      if (!rapid && i < count - 1) {
        await new Promise(resolve => setTimeout(resolve, 100))
      }

    } catch (error) {
      failed++
      console.error(`💥 Vote ${i + 1}/${count} error:`, error)
    }
  }

  const duration = Date.now() - startTime
  const rate = (successful / (duration / 1000)).toFixed(2)

  console.log(`\n📊 Results:`)
  console.log(`   Successful: ${successful}`)
  console.log(`   Failed: ${failed}`)
  console.log(`   Duration: ${duration}ms`)
  console.log(`   Rate: ${rate} votes/second`)
}

async function showStatus() {
  try {
    const status = await VoteProducer.getQueueStatus()
    console.log('\n📊 Queue Status:')
    console.log(`   Stream Length: ${status.streamLength}`)
    console.log(`   Pending Messages: ${status.pendingMessages}`)
    console.log(`   Consumer Groups: ${status.consumerGroups}`)

    // Show database vote count
    const voteCount = await prisma.vote.count()
    console.log(`   Total Votes in DB: ${voteCount}`)
  } catch (error) {
    console.error('❌ Failed to get status:', error)
  }
}

function showInfo() {
  if (!testData) {
    console.log('❌ Test data not initialized')
    return
  }

  console.log('\n📋 Test Data Info:')
  console.log(`   Tournament: ${testData.tournament.title}`)
  console.log(`   Contestants: ${testData.contestants.length}`)
  console.log(`   Voters: ${testData.voters.length}`)
  console.log(`   Tournament ID: ${testData.tournament.id}`)
}

function showHelp() {
  console.log('\n📝 Available commands:')
  console.log('   vote <count>     - Send <count> random votes (default: 1)')
  console.log('   burst <count>    - Send <count> votes rapidly (no delays)')
  console.log('   status          - Show queue status')
  console.log('   info            - Show test data info')
  console.log('   help            - Show this help')
  console.log('   exit            - Exit the producer')
  console.log('\n💡 Examples:')
  console.log('   vote 5          - Send 5 votes with delays')
  console.log('   burst 20        - Send 20 votes rapidly')
  console.log('   status          - Check queue status')
}

async function startInteractiveMode() {
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout,
    prompt: '🎯 producer> '
  })

  rl.prompt()

  rl.on('line', async (input) => {
    const [command, ...args] = input.trim().split(' ')

    switch (command.toLowerCase()) {
      case 'vote':
        const voteCount = parseInt(args[0]) || 1
        await sendVotes(voteCount, false)
        break

      case 'burst':
        const burstCount = parseInt(args[0]) || 1
        await sendVotes(burstCount, true)
        break

      case 'status':
        await showStatus()
        break

      case 'info':
        showInfo()
        break

      case 'help':
        showHelp()
        break

      case 'exit':
      case 'quit':
        console.log('👋 Goodbye!')
        await cleanup()
        process.exit(0)

      case '':
        // Empty command, just show prompt again
        break

      default:
        console.log(`❌ Unknown command: ${command}`)
        console.log('💡 Type "help" for available commands')
        break
    }

    rl.prompt()
  })

  rl.on('close', async () => {
    console.log('\n👋 Goodbye!')
    await cleanup()
    process.exit(0)
  })
}

async function cleanup() {
  try {
    await redisManager.shutdown()
    await prisma.$disconnect()
    console.log('✅ Cleanup complete')
  } catch (error) {
    console.error('❌ Error during cleanup:', error)
  }
}

// Handle process signals
process.on('SIGINT', async () => {
  console.log('\n🛑 Received SIGINT, shutting down...')
  await cleanup()
  process.exit(0)
})

process.on('SIGTERM', async () => {
  console.log('\n🛑 Received SIGTERM, shutting down...')
  await cleanup()
  process.exit(0)
})

// Start the producer
if (require.main === module) {
  initializeProducer().then(() => {
    startInteractiveMode()
  }).catch((error) => {
    console.error('💥 Failed to start producer:', error)
    process.exit(1)
  })
}
