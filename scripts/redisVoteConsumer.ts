#!/usr/bin/env ts-node
import { redisManager } from '../src/redis/RedisConfig'
import { voteConsumer } from '../src/redis/VoteConsumer'
import { prisma } from '../src/providers/PrismaProvider'

async function startVoteConsumer() {
  console.log('🚀 Redis Vote Consumer Starting...')
  console.log('=====================================')
  
  try {
    // Start the vote consumer
    console.log('🔄 Starting vote consumer...')
    await voteConsumer.start()
    console.log('✅ Vote consumer started successfully')

    // Display status information
    console.log('\n📊 Consumer Status:')
    const status = await voteConsumer.getStatus()
    console.log(`   Running: ${status.isRunning}`)
    console.log(`   Redis Health: ${status.redisHealth.status}`)
    console.log(`   Stream Length: ${status.queueMetrics.streamLength}`)
    console.log(`   Pending Messages: ${status.queueMetrics.pendingMessages}`)

    console.log('\n🎯 Consumer is now running and processing votes...')
    console.log('📝 You can now run the producer script in another terminal')
    console.log('⏹️  Press Ctrl+C to stop the consumer')

    // Keep the process running
    process.on('SIGINT', async () => {
      console.log('\n🛑 Received SIGINT, shutting down gracefully...')
      await gracefulShutdown()
    })

    process.on('SIGTERM', async () => {
      console.log('\n🛑 Received SIGTERM, shutting down gracefully...')
      await gracefulShutdown()
    })

    // Display periodic status updates
    setInterval(async () => {
      try {
        const currentStatus = await voteConsumer.getStatus()
        const timestamp = new Date().toLocaleTimeString()
        
        console.log(`\n[${timestamp}] 📊 Status Update:`)
        console.log(`   Stream Length: ${currentStatus.queueMetrics.streamLength}`)
        console.log(`   Pending Messages: ${currentStatus.queueMetrics.pendingMessages}`)
        
        // Show vote count from database
        const voteCount = await prisma.vote.count()
        console.log(`   Total Votes in DB: ${voteCount}`)
        
      } catch (error) {
        console.error('❌ Error getting status:', error)
      }
    }, 60000) // Update every 60 seconds

    // Keep process alive
    await new Promise(() => {}) // This will run indefinitely until interrupted

  } catch (error) {
    console.error('💥 Failed to start vote consumer:', error)
    process.exit(1)
  }
}

async function gracefulShutdown() {
  console.log('🔄 Shutting down vote consumer...')
  
  try {
    await voteConsumer.gracefulShutdown(10000) // 10 second timeout
    await prisma.$disconnect()
    console.log('✅ Vote consumer shutdown complete')
    process.exit(0)
  } catch (error) {
    console.error('❌ Error during shutdown:', error)
    process.exit(1)
  }
}

// Handle uncaught exceptions
process.on('uncaughtException', async (error) => {
  console.error('💥 Uncaught Exception:', error)
  await gracefulShutdown()
})

process.on('unhandledRejection', async (reason, promise) => {
  console.error('💥 Unhandled Rejection at:', promise, 'reason:', reason)
  await gracefulShutdown()
})

// Start the consumer
if (require.main === module) {
  startVoteConsumer()
}
