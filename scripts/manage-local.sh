#!/bin/bash

# Local Docker Management Script
# Provides various management commands for local Docker deployment

set -e

COMPOSE_FILE="docker-compose.local.yml"
ENV_FILE=".env.local"

case "$1" in
    "start")
        echo "🚀 Starting all services..."
        docker-compose -f $COMPOSE_FILE --env-file $ENV_FILE up -d

        echo "⏳ Waiting for database to be ready..."
        sleep 5

        echo "🗄️ Setting up database schema..."
        if docker-compose -f $COMPOSE_FILE exec -T api-server npx prisma db push > /dev/null 2>&1; then
            echo "✅ Database schema created successfully!"
        else
            echo "⚠️  Database schema setup failed, but continuing..."
            echo "   This might require manual intervention for production-like safety"
            echo "   You can manually run: bash scripts/manage-local.sh db-push"
        fi

        echo "✅ All services started"
        echo ""
        echo "🌐 Services available at:"
        echo "  API Server: http://localhost:8080"
        echo "  Database: localhost:5432"
        echo "  Redis: localhost:6379"
        ;;
    "logs")
        echo "📋 Showing logs for all services..."
        docker-compose -f $COMPOSE_FILE logs -f
        ;;
    "logs-api")
        echo "📋 Showing API server logs..."
        docker-compose -f $COMPOSE_FILE logs -f api-server
        ;;
    "logs-consumer")
        echo "📋 Showing vote consumer logs..."
        docker-compose -f $COMPOSE_FILE logs -f vote-consumer
        ;;
    "logs-db")
        echo "📋 Showing database logs..."
        docker-compose -f $COMPOSE_FILE logs -f db
        ;;
    "logs-redis")
        echo "📋 Showing Redis logs..."
        docker-compose -f $COMPOSE_FILE logs -f redis
        ;;
    "stop")
        echo "🛑 Stopping all services..."
        # Stop Prisma Studio first if it's running
        if docker ps --format "{{.Names}}" | grep -q "getrankt-studio-local"; then
            echo "🎨 Stopping Prisma Studio..."
            docker-compose -f $COMPOSE_FILE stop prisma-studio
            docker-compose -f $COMPOSE_FILE rm -f prisma-studio
        fi
        # Stop all other services
        docker-compose -f $COMPOSE_FILE --env-file $ENV_FILE down
        echo "✅ All services stopped"
        ;;
    "restart")
        echo "🔄 Restarting all services..."
        docker-compose -f $COMPOSE_FILE --env-file $ENV_FILE down
        docker-compose -f $COMPOSE_FILE --env-file $ENV_FILE up -d
        echo "✅ All services restarted"
        ;;
    "restart-api")
        echo "🔄 Restarting API server..."
        docker-compose -f $COMPOSE_FILE restart api-server
        echo "✅ API server restarted"
        ;;
    "restart-consumer")
        echo "🔄 Restarting vote consumer..."
        docker-compose -f $COMPOSE_FILE restart vote-consumer
        echo "✅ Vote consumer restarted"
        ;;
    "status")
        echo "📊 Service status:"
        docker-compose -f $COMPOSE_FILE ps
        ;;
    "health")
        echo "🔍 Checking service health..."
        echo "Database:"
        docker-compose -f $COMPOSE_FILE exec db pg_isready -U admin -d getrankt || echo "❌ Database not ready"
        echo "Redis:"
        docker-compose -f $COMPOSE_FILE exec redis redis-cli ping || echo "❌ Redis not ready"
        echo "API Server:"
        curl -f http://localhost:8080/health && echo " ✅ API healthy" || echo "❌ API not responding"
        ;;
    "studio")
        echo "🎨 Starting Prisma Studio..."
        docker-compose -f $COMPOSE_FILE --env-file $ENV_FILE --profile tools up -d prisma-studio
        echo "✅ Prisma Studio available at http://localhost:5555"
        ;;
    "studio-stop")
        echo "🛑 Stopping Prisma Studio..."
        docker-compose -f $COMPOSE_FILE stop prisma-studio
        docker-compose -f $COMPOSE_FILE rm -f prisma-studio
        echo "✅ Prisma Studio stopped"
        ;;
    "migrate")
        echo "🗄️ Running database migrations..."
        docker-compose -f $COMPOSE_FILE exec api-server npx prisma migrate deploy
        echo "✅ Migrations completed"
        ;;
    "db-push")
        echo "🗄️ Pushing database schema..."
        docker-compose -f $COMPOSE_FILE exec api-server npx prisma db push
        echo "✅ Database schema pushed successfully!"
        ;;
    "seed")
        echo "🌱 Seeding database..."
        docker-compose -f $COMPOSE_FILE exec api-server npm run init:testdata
        echo "✅ Database seeded"
        ;;
    "clean")
        echo "🧹 Cleaning up containers and volumes..."
        docker-compose -f $COMPOSE_FILE --env-file $ENV_FILE down -v
        docker system prune -f
        echo "✅ Cleanup completed"
        ;;
    "rebuild")
        echo "🔨 Rebuilding and restarting services..."
        docker-compose -f $COMPOSE_FILE --env-file $ENV_FILE down
        docker build -t getrankt-backend:local .
        docker-compose -f $COMPOSE_FILE --env-file $ENV_FILE up -d
        echo "✅ Services rebuilt and restarted"
        ;;
    *)
        echo "🐳 GetRankt Local Docker Management"
        echo "Usage: $0 {command}"
        echo ""
        echo "Available commands:"
        echo "  start         - Start all services (automatically sets up database schema)"
        echo "  logs          - Show logs for all services"
        echo "  logs-api      - Show API server logs"
        echo "  logs-consumer - Show vote consumer logs"
        echo "  logs-db       - Show database logs"
        echo "  logs-redis    - Show Redis logs"
        echo "  stop          - Stop all services"
        echo "  restart       - Restart all services"
        echo "  restart-api   - Restart API server only"
        echo "  restart-consumer - Restart vote consumer only"
        echo "  status        - Show service status"
        echo "  health        - Check service health"
        echo "  studio        - Start Prisma Studio"
        echo "  studio-stop   - Stop Prisma Studio"
        echo "  migrate       - Run database migrations"
        echo "  db-push       - Push database schema (creates tables)"
        echo "  seed          - Seed database with test data"
        echo "  clean         - Clean up containers and volumes"
        echo "  rebuild       - Rebuild and restart services"
        echo ""
        exit 1
        ;;
esac
