# Development Docker Environment Example
# Copy this file to .env.dev for development Docker deployment

# ==================== APPLICATION CONFIGURATION ====================
NODE_ENV=development
PORT=4000
HOST=0.0.0.0

# ==================== DATABASE CONFIGURATION ====================
# Database credentials for development Docker deployment
DB_USER=admin
DB_PASSWORD=admin
DB_HOST=db
DB_PORT=5432
DB_NAME=getrankt_dev
DB_SCHEMA=public

# Constructed DATABASE_URL for Prisma
DATABASE_URL=postgresql://${DB_USER}:${DB_PASSWORD}@${DB_HOST}:${DB_PORT}/${DB_NAME}?schema=${DB_SCHEMA}

# Connection Pool Configuration (smaller for dev)
DB_CONNECTION_LIMIT=5
DB_POOL_TIMEOUT=20

# ==================== AUTHENTICATION CONFIGURATION ====================
# Development JWT secret (not secure, only for development)
JWT_SECRET=dev_jwt_secret_not_for_production_use_only

# ==================== REDIS CONFIGURATION ====================
REDIS_URL=redis://redis:6379
REDIS_RETRY_ATTEMPTS=3
REDIS_RETRY_DELAY=5000
REDIS_CONNECTION_TIMEOUT=10000

# ==================== LOGGING CONFIGURATION ====================
LOG_LEVEL=debug

# ==================== DEVELOPMENT NOTES ====================
# 1. This is for DEVELOPMENT Docker deployment only
# 2. Uses separate database (getrankt_dev) to avoid conflicts
# 3. Services are accessible on localhost
# 4. Database: localhost:5433 (different port to avoid conflicts)
# 5. API: localhost:4000
# 6. Redis: localhost:6380 (different port to avoid conflicts)
# 7. Prisma Studio: localhost:5556 (if enabled)
# 8. Hot reload enabled via nodemon
