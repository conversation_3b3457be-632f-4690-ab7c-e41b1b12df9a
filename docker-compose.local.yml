# Local Docker Deployment - Production-like environment

services:
  # PostgreSQL Database
  db:
    image: postgres:16
    container_name: getrankt-db-local
    restart: unless-stopped
    environment:
      POSTGRES_USER: ${DB_USER:-admin}
      POSTGRES_PASSWORD: ${DB_PASSWORD:-admin}
      POSTGRES_DB: ${DB_NAME:-getrankt}
      POSTGRES_HOST_AUTH_METHOD: md5
    volumes:
      - pgdata_local:/var/lib/postgresql/data
    ports:
      - "127.0.0.1:5434:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${DB_USER:-admin} -d ${DB_NAME:-getrankt}"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    networks:
      - getrankt-network

  # Redis for vote processing
  redis:
    image: redis:7-alpine
    container_name: getrankt-redis-local
    restart: unless-stopped
    command: redis-server --appendonly yes --maxmemory 512mb --maxmemory-policy allkeys-lru
    volumes:
      - redis_data_local:/data
    ports:
      - "127.0.0.1:6380:6379"
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s
    networks:
      - getrankt-network

  # API Server
  api-server:
    image: getrankt-backend:local
    container_name: getrankt-api-local
    restart: unless-stopped
    env_file:
      - .env.local
    environment:
      - NODE_ENV=production
      - PORT=8080
      - HOST=0.0.0.0
      - SERVICE_TYPE=api
      # Database configuration
      - DB_USER=${DB_USER:-admin}
      - DB_PASSWORD=${DB_PASSWORD:-admin}
      - DB_HOST=db
      - DB_PORT=5432
      - DB_NAME=${DB_NAME:-getrankt}
      - DB_SCHEMA=public
      - DATABASE_URL=postgresql://${DB_USER:-admin}:${DB_PASSWORD:-admin}@db:5432/${DB_NAME:-getrankt}?schema=public
      - DB_CONNECTION_LIMIT=10
      - DB_POOL_TIMEOUT=20
      # Redis configuration
      - REDIS_URL=redis://redis:6379
      - REDIS_RETRY_ATTEMPTS=3
      - REDIS_RETRY_DELAY=5000
      - REDIS_CONNECTION_TIMEOUT=10000
      # Authentication
      - JWT_SECRET=${JWT_SECRET:-your_super_secure_jwt_secret_change_this_in_production}
    ports:
      - "8080:8080"
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "node", "healthcheck.js"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    networks:
      - getrankt-network
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M

  # Vote Consumer (Background Worker)
  vote-consumer:
    image: getrankt-backend:local
    container_name: getrankt-consumer-local
    restart: unless-stopped
    command: ["npm", "run", "redis:consumer"]
    environment:
      - NODE_ENV=production
      - SERVICE_TYPE=consumer
      # Database configuration
      - DB_USER=${DB_USER:-admin}
      - DB_PASSWORD=${DB_PASSWORD:-admin}
      - DB_HOST=db
      - DB_PORT=5432
      - DB_NAME=${DB_NAME:-getrankt}
      - DB_SCHEMA=public
      - DATABASE_URL=postgresql://${DB_USER:-admin}:${DB_PASSWORD:-admin}@db:5432/${DB_NAME:-getrankt}?schema=public
      - DB_CONNECTION_LIMIT=5
      - DB_POOL_TIMEOUT=30
      # Redis configuration
      - REDIS_URL=redis://redis:6379
      - REDIS_RETRY_ATTEMPTS=5
      - REDIS_RETRY_DELAY=3000
      - REDIS_CONNECTION_TIMEOUT=15000
      # Authentication (needed for database operations)
      - JWT_SECRET=${JWT_SECRET:-your_super_secure_jwt_secret_change_this_in_production}
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "node", "healthcheck.js"]
      interval: 60s
      timeout: 10s
      retries: 3
      start_period: 60s
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    networks:
      - getrankt-network
    deploy:
      resources:
        limits:
          memory: 256M
        reservations:
          memory: 128M

  # Prisma Studio (Optional - for database management)
  prisma-studio:
    build: .
    container_name: getrankt-studio-local
    restart: unless-stopped
    command: ["npx", "prisma", "studio", "--hostname", "0.0.0.0"]
    environment:
      - DATABASE_URL=postgresql://${DB_USER:-admin}:${DB_PASSWORD:-admin}@db:5432/${DB_NAME:-getrankt}?schema=public
    ports:
      - "127.0.0.1:5555:5555"
    depends_on:
      db:
        condition: service_healthy
    networks:
      - getrankt-network
    profiles:
      - tools

volumes:
  pgdata_local:
    driver: local
  redis_data_local:
    driver: local

networks:
  getrankt-network:
    driver: bridge
