{"name": "getrankt-backend", "version": "1.0.0", "main": "app.js", "scripts": {"dev": "echo '⚠️  For local development, use: npm run dev:docker (recommended)' && echo '   Or setup local services first: npm run dev:setup' && exit 1", "dev:local": "nodemon src/app.ts", "dev:setup": "echo '🔧 Setting up local development environment...' && echo '1. Start PostgreSQL: brew services start postgresql@14' && echo '2. Start Redis: brew services start redis' && echo '3. Create database: createdb getrankt_dev' && echo '4. Setup schema: npx prisma db push' && echo '5. Run: npm run dev:local'", "build": "tsc", "start": "node dist/src/app.js", "redis:consumer": "node dist/scripts/redisVoteConsumer.js", "redis:consumer:dev": "ts-node scripts/redisVoteConsumer.ts", "redis:producer": "ts-node scripts/redisVoteProducer.ts", "local:start": "bash scripts/manage-local.sh start", "local:stop": "bash scripts/manage-local.sh stop", "local:restart": "bash scripts/manage-local.sh restart", "local:status": "bash scripts/manage-local.sh status", "local:db-push": "bash scripts/manage-local.sh db-push", "local:studio": "bash scripts/manage-local.sh studio", "local:studio:stop": "bash scripts/manage-local.sh studio-stop", "local:logs:api": "bash scripts/manage-local.sh logs-api", "local:logs:consumer": "bash scripts/manage-local.sh logs-consumer", "dev:docker": "bash scripts/manage-dev.sh start", "dev:docker:stop": "bash scripts/manage-dev.sh stop", "dev:docker:restart": "bash scripts/manage-dev.sh restart", "dev:docker:logs": "bash scripts/manage-dev.sh logs", "dev:docker:logs:api": "bash scripts/manage-dev.sh logs-api", "dev:docker:logs:consumer": "bash scripts/manage-dev.sh logs-consumer", "dev:docker:logs:db": "bash scripts/manage-dev.sh logs-db", "dev:docker:status": "bash scripts/manage-dev.sh status", "dev:docker:build": "bash scripts/manage-dev.sh build", "dev:docker:db-push": "bash scripts/manage-dev.sh db-push", "dev:docker:studio": "bash scripts/manage-dev.sh studio", "dev:docker:studio:stop": "bash scripts/manage-dev.sh studio-stop", "test:create-tournaments": "ts-node scripts/createTestTournaments.ts", "test:cleanup": "ts-node scripts/cleanupTestData.ts"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@prisma/client": "^6.12.0", "@types/node-fetch": "^2.6.12", "@types/uuid": "^10.0.0", "bcrypt": "^6.0.0", "dotenv": "^16.5.0", "express": "^5.1.0", "express-rate-limit": "^8.0.1", "express-validator": "^7.2.1", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "node-fetch": "^3.3.2", "redis": "^5.6.1", "uuid": "^11.1.0"}, "devDependencies": {"@types/bcrypt": "^5.0.2", "@types/express": "^5.0.2", "@types/express-rate-limit": "^5.1.3", "@types/jsonwebtoken": "^9.0.9", "@types/node": "^22.15.21", "@types/redis": "^4.0.10", "nodemon": "^3.1.10", "prisma": "^6.12.0", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}