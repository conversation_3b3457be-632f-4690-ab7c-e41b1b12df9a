{"openapi": "3.0.0", "info": {"title": "GetRankt API", "description": "API for the GetRankt tournament platform with Elo-based voting system", "version": "1.0.0", "contact": {"name": "GetRankt Support", "email": "<EMAIL>"}}, "servers": [{"url": "http://localhost:4000", "description": "Development server"}], "components": {"securitySchemes": {"bearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT"}}, "schemas": {"User": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "email": {"type": "string", "format": "email"}, "username": {"type": "string"}, "avatarUrl": {"type": "string", "nullable": true}, "coins": {"type": "integer", "default": 0}, "leagueLevel": {"type": "integer", "default": 1}, "createdAt": {"type": "string", "format": "date-time"}}}, "Tournament": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "title": {"type": "string"}, "category": {"type": "string"}, "createdById": {"type": "string", "format": "uuid"}, "entryFee": {"type": "integer"}, "status": {"type": "string", "enum": ["open", "closed", "completed"], "default": "open"}, "createdAt": {"type": "string", "format": "date-time"}, "maxContestants": {"type": "integer", "default": 64}}}, "Submission": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "userId": {"type": "string", "format": "uuid"}, "tournamentId": {"type": "string", "format": "uuid"}, "videoUrl": {"type": "string"}, "votes": {"type": "integer", "default": 0}, "round": {"type": "integer", "default": 1}, "eloScore": {"type": "integer", "default": 1000}, "wins": {"type": "integer", "default": 0}, "losses": {"type": "integer", "default": 0}, "votesReceived": {"type": "integer", "default": 0}, "createdAt": {"type": "string", "format": "date-time"}}}, "EloVote": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "voterId": {"type": "string", "format": "uuid"}, "tournamentId": {"type": "string", "format": "uuid"}, "winnerSubmissionId": {"type": "string", "format": "uuid"}, "loserSubmissionId": {"type": "string", "format": "uuid"}, "winnerScoreBefore": {"type": "integer"}, "winnerScoreAfter": {"type": "integer"}, "loserScoreBefore": {"type": "integer"}, "loserScoreAfter": {"type": "integer"}, "createdAt": {"type": "string", "format": "date-time"}}}}}, "paths": {"/api/auth/register": {"post": {"summary": "Register a new user", "tags": ["Authentication"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["email", "password", "username"], "properties": {"email": {"type": "string", "format": "email"}, "password": {"type": "string", "minLength": 6}, "username": {"type": "string", "minLength": 3, "maxLength": 20, "pattern": "^[a-zA-Z0-9_]+$", "description": "Username can only contain letters, numbers, and underscores"}}}}}}, "responses": {"201": {"description": "User registered successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"token": {"type": "string"}, "user": {"$ref": "#/components/schemas/User"}}}, "message": {"type": "string", "example": "User registered successfully"}}}}}}, "400": {"description": "Invalid input - email already exists, username already exists, or validation failed"}}}}, "/api/auth/login": {"post": {"summary": "Login a user", "tags": ["Authentication"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["email", "password"], "properties": {"email": {"type": "string", "format": "email"}, "password": {"type": "string"}}}}}}, "responses": {"200": {"description": "Login successful", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"token": {"type": "string"}, "user": {"$ref": "#/components/schemas/User"}}}, "message": {"type": "string", "example": "Login successful"}}}}}}, "401": {"description": "Invalid credentials"}}}}, "/api/auth/check-username/{username}": {"get": {"summary": "Check username availability", "tags": ["Authentication"], "parameters": [{"name": "username", "in": "path", "required": true, "schema": {"type": "string", "minLength": 3, "maxLength": 20, "pattern": "^[a-zA-Z0-9_]+$"}, "description": "Username to check availability for"}], "responses": {"200": {"description": "Username availability checked", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"available": {"type": "boolean"}}}, "message": {"type": "string", "example": "Username is available"}}}}}}, "400": {"description": "Invalid username format"}}}}, "/api/auth/profile": {"post": {"summary": "Get user profile by ID", "tags": ["Authentication"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["userId"], "properties": {"userId": {"type": "string", "format": "uuid", "description": "User ID (UUID) to get profile for", "example": "123e4567-e89b-12d3-a456-************"}}}}}}, "responses": {"200": {"description": "User profile retrieved successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"user": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "username": {"type": "string"}, "avatarUrl": {"type": "string", "nullable": true}, "coins": {"type": "integer"}, "leagueLevel": {"type": "integer"}, "createdAt": {"type": "string", "format": "date-time"}}}}}, "message": {"type": "string", "example": "User profile fetched successfully"}}}}}}, "400": {"description": "User ID is required or invalid user ID format"}, "401": {"description": "Unauthorized - Missing or invalid token"}, "404": {"description": "User not found"}, "500": {"description": "Failed to fetch user profile"}}}}, "/api/tournaments": {"get": {"summary": "Get all tournaments", "tags": ["Tournaments"], "responses": {"200": {"description": "List of tournaments", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"tournaments": {"type": "array", "items": {"$ref": "#/components/schemas/Tournament"}}}}, "message": {"type": "string", "example": "Tournaments retrieved successfully"}}}}}}}}, "post": {"summary": "Create a new tournament", "tags": ["Tournaments"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["title", "category", "entryFee", "max<PERSON><PERSON><PERSON><PERSON>"], "properties": {"title": {"type": "string"}, "category": {"type": "string"}, "entryFee": {"type": "integer", "minimum": 0}, "maxContestants": {"type": "integer", "minimum": 2, "description": "Maximum number of contestants (must be a power of 2)"}}}}}}, "responses": {"201": {"description": "Tournament created successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"tournament": {"$ref": "#/components/schemas/Tournament"}}}, "message": {"type": "string", "example": "Tournament created successfully"}}}}}}, "400": {"description": "Invalid input"}, "401": {"description": "Unauthorized"}}}}, "/api/tournaments/{id}": {"get": {"summary": "Get tournament by ID", "tags": ["Tournaments"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Tournament details", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"tournament": {"$ref": "#/components/schemas/Tournament"}}}, "message": {"type": "string", "example": "Tournament retrieved successfully"}}}}}}, "404": {"description": "Tournament not found"}}}}, "/api/tournaments/{id}/submit": {"post": {"summary": "Submit to a tournament", "tags": ["Submissions"], "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["videoUrl"], "properties": {"videoUrl": {"type": "string"}}}}}}, "responses": {"201": {"description": "Submission created successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"submission": {"$ref": "#/components/schemas/Submission"}}}, "message": {"type": "string", "example": "Submission created successfully"}}}}}}, "400": {"description": "Invalid input or user already has a submission"}, "401": {"description": "Unauthorized"}, "404": {"description": "Tournament not found"}}}}, "/api/tournaments/{id}/elo/matchup": {"get": {"summary": "Get next Elo matchup", "tags": ["Elo Voting"], "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Next matchup retrieved successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"matchup": {"type": "object", "properties": {"contestant1": {"$ref": "#/components/schemas/Submission"}, "contestant2": {"$ref": "#/components/schemas/Submission"}}}}}, "message": {"type": "string", "example": "Matchup retrieved successfully"}}}}}}, "404": {"description": "No available matchups found"}}}}, "/api/tournaments/{id}/elo/vote": {"post": {"summary": "Submit an Elo vote", "tags": ["Elo Voting"], "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["winnerId", "loserId"], "properties": {"winnerId": {"type": "string", "format": "uuid"}, "loserId": {"type": "string", "format": "uuid"}}}}}}, "responses": {"200": {"description": "Vote recorded successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"result": {"type": "object", "properties": {"winnerNewScore": {"type": "integer"}, "loserNewScore": {"type": "integer"}, "expectedWinProbability": {"type": "number"}}}, "message": {"type": "string"}}}, "message": {"type": "string", "example": "Elo vote recorded successfully"}}}}}}, "400": {"description": "Invalid input"}, "401": {"description": "Unauthorized"}}}}, "/api/tournaments/{id}/elo/results": {"get": {"summary": "Get Elo results", "tags": ["Elo Voting"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Elo results retrieved successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"results": {"type": "array", "items": {"$ref": "#/components/schemas/Submission"}}, "totalContestants": {"type": "integer"}}}, "message": {"type": "string", "example": "Elo results retrieved successfully"}}}}}}, "404": {"description": "Tournament not found"}}}}, "/api/tournaments/random/matchup": {"get": {"summary": "Get random matchup from any tournament", "tags": ["Matchups"], "security": [{"bearerAuth": []}], "responses": {"200": {"description": "Random matchup retrieved successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"matchup": {"type": "object", "properties": {"contestant1": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "userId": {"type": "string", "format": "uuid"}, "videoUrl": {"type": "string"}, "user": {"type": "object", "properties": {"username": {"type": "string"}, "avatarUrl": {"type": "string", "nullable": true}}}}}, "contestant2": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "userId": {"type": "string", "format": "uuid"}, "videoUrl": {"type": "string"}, "user": {"type": "object", "properties": {"username": {"type": "string"}, "avatarUrl": {"type": "string", "nullable": true}}}}}, "tournamentId": {"type": "string", "format": "uuid"}, "tournamentTitle": {"type": "string"}}}}}, "message": {"type": "string", "example": "Random matchup retrieved successfully"}}}}}}, "404": {"description": "No available matchups found"}, "401": {"description": "Unauthorized"}}}}, "/api/tournaments/{id}/random-matchup": {"get": {"summary": "Get random matchup from a specific tournament", "tags": ["Matchups"], "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "required": true, "description": "Tournament ID", "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Tournament random matchup retrieved successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"matchup": {"type": "object", "properties": {"contestant1": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "userId": {"type": "string", "format": "uuid"}, "videoUrl": {"type": "string"}, "user": {"type": "object", "properties": {"username": {"type": "string"}, "avatarUrl": {"type": "string", "nullable": true}}}}}, "contestant2": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "userId": {"type": "string", "format": "uuid"}, "videoUrl": {"type": "string"}, "user": {"type": "object", "properties": {"username": {"type": "string"}, "avatarUrl": {"type": "string", "nullable": true}}}}}, "tournamentId": {"type": "string", "format": "uuid"}, "tournamentTitle": {"type": "string"}}}}}, "message": {"type": "string", "example": "Tournament random matchup retrieved successfully"}}}}}}, "404": {"description": "No available matchups found for this tournament or tournament not found"}, "401": {"description": "Unauthorized"}}}}}}