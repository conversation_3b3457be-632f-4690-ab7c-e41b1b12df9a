# Development Docker Deployment - Development environment with hot reload

services:
  # PostgreSQL Database (Development)
  db:
    image: postgres:16
    container_name: getrankt-db-dev
    restart: unless-stopped
    environment:
      POSTGRES_USER: ${DB_USER:-admin}
      POSTGRES_PASSWORD: ${DB_PASSWORD:-admin}
      POSTGRES_DB: ${DB_NAME:-getrankt_dev}
      POSTGRES_HOST_AUTH_METHOD: md5
    volumes:
      - pgdata_dev:/var/lib/postgresql/data
    ports:
      - "127.0.0.1:5431:5432"  # Different port to avoid conflicts with local/production
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${DB_USER:-admin} -d ${DB_NAME:-getrankt_dev}"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    networks:
      - getrankt-dev-network

  # Redis for vote processing (Development)
  redis:
    image: redis:7-alpine
    container_name: getrankt-redis-dev
    restart: unless-stopped
    command: redis-server --appendonly yes --maxmemory 256mb --maxmemory-policy allkeys-lru
    volumes:
      - redis_data_dev:/data
    ports:
      - "127.0.0.1:6380:6379"  # Different port to avoid conflicts
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s
    networks:
      - getrankt-dev-network

  # API Server (Development with hot reload)
  api-server:
    build:
      context: .
      target: development
    container_name: getrankt-api-dev
    restart: unless-stopped
    command: ["npm", "run", "dev:local"]
    env_file:
      - .env.dev
    environment:
      - NODE_ENV=development
      - PORT=4000
      - HOST=0.0.0.0
      - SERVICE_TYPE=api
      # Database configuration
      - DB_USER=${DB_USER:-admin}
      - DB_PASSWORD=${DB_PASSWORD:-admin}
      - DB_HOST=db
      - DB_PORT=5432
      - DB_NAME=${DB_NAME:-getrankt_dev}
      - DB_SCHEMA=public
      - DATABASE_URL=postgresql://${DB_USER:-admin}:${DB_PASSWORD:-admin}@db:5432/${DB_NAME:-getrankt_dev}?schema=public
      - DB_CONNECTION_LIMIT=5
      - DB_POOL_TIMEOUT=20
      # Redis configuration
      - REDIS_URL=redis://redis:6379
      - REDIS_RETRY_ATTEMPTS=3
      - REDIS_RETRY_DELAY=5000
      - REDIS_CONNECTION_TIMEOUT=10000
      # Authentication
      - JWT_SECRET=${JWT_SECRET:-dev_jwt_secret_not_for_production}
    volumes:
      # Mount source code for hot reload
      - ./src:/usr/src/app/src:ro
      - ./prisma:/usr/src/app/prisma:ro
      - ./package.json:/usr/src/app/package.json:ro
      - ./tsconfig.json:/usr/src/app/tsconfig.json:ro
      # Exclude node_modules to use container's version
      - /usr/src/app/node_modules
    ports:
      - "4000:4000"
      - "5556:5555"  # Prisma Studio
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - getrankt-dev-network

  # Vote Consumer (Development)
  vote-consumer:
    build:
      context: .
      target: development
    container_name: getrankt-consumer-dev
    restart: unless-stopped
    command: ["npm", "run", "redis:consumer:dev"]
    environment:
      - NODE_ENV=development
      - SERVICE_TYPE=consumer
      # Database configuration
      - DB_USER=${DB_USER:-admin}
      - DB_PASSWORD=${DB_PASSWORD:-admin}
      - DB_HOST=db
      - DB_PORT=5432
      - DB_NAME=${DB_NAME:-getrankt_dev}
      - DB_SCHEMA=public
      - DATABASE_URL=postgresql://${DB_USER:-admin}:${DB_PASSWORD:-admin}@db:5432/${DB_NAME:-getrankt_dev}?schema=public
      - DB_CONNECTION_LIMIT=3
      - DB_POOL_TIMEOUT=30
      # Redis configuration
      - REDIS_URL=redis://redis:6379
      - REDIS_RETRY_ATTEMPTS=5
      - REDIS_RETRY_DELAY=3000
      - REDIS_CONNECTION_TIMEOUT=15000
      # Authentication
      - JWT_SECRET=${JWT_SECRET:-dev_jwt_secret_not_for_production}
    volumes:
      # Mount source code for hot reload
      - ./src:/usr/src/app/src:ro
      - ./scripts:/usr/src/app/scripts:ro
      - ./prisma:/usr/src/app/prisma:ro
      - ./package.json:/usr/src/app/package.json:ro
      - ./tsconfig.json:/usr/src/app/tsconfig.json:ro
      # Exclude node_modules to use container's version
      - /usr/src/app/node_modules
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - getrankt-dev-network

  # Prisma Studio (Development)
  prisma-studio:
    build:
      context: .
      target: development
    container_name: getrankt-studio-dev
    restart: unless-stopped
    command: ["npx", "prisma", "studio", "--hostname", "0.0.0.0"]
    environment:
      - DATABASE_URL=postgresql://${DB_USER:-admin}:${DB_PASSWORD:-admin}@db:5432/${DB_NAME:-getrankt_dev}?schema=public
    ports:
      - "127.0.0.1:5556:5555"  # Different port to avoid conflicts
    depends_on:
      db:
        condition: service_healthy
    networks:
      - getrankt-dev-network
    profiles:
      - tools

volumes:
  pgdata_dev:
    driver: local
  redis_data_dev:
    driver: local

networks:
  getrankt-dev-network:
    driver: bridge
