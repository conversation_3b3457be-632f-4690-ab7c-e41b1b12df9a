generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id                 String            @id @default(uuid())
  email              String            @unique
  passwordHash       String
  username           String            @unique
  avatarUrl          String?
  coins              Int               @default(0)
  leagueLevel        Int               @default(1)
  createdAt          DateTime          @default(now())
  coinTransactions   CoinTransaction[]
  submissions        Submission[]
  createdTournaments Tournament[]      @relation("UserCreatedTournaments")
  votes              Vote[]
}

model Tournament {
  id                String            @id @default(uuid())
  title             String
  category          String
  createdById       String
  entryFee          Int
  status            String            @default("open")
  createdAt         DateTime          @default(now())
  currentBracket    Int               @default(1)
  maxContestants    Int               @default(64)
  coinTransactions  CoinTransaction[]
  matchups          Matchup[]
  submissions       Submission[]
  createdBy         User              @relation("UserCreatedTournaments", fields: [createdById], references: [id])
  votes             Vote[]
}

model Submission {
  id               String     @id @default(uuid())
  userId           String
  tournamentId     String
  videoUrl         String
  votes            Int        @default(0)
  createdAt        DateTime   @default(now())
  bracketPosition  Int?
  currentBracket   Int        @default(1)
  eliminatedAt     DateTime?
  status           String     @default("active")
  matchupsAsFirst  Matchup[]  @relation("FirstSubmission")
  matchupsAsSecond Matchup[]  @relation("SecondSubmission")
  tournament       Tournament @relation(fields: [tournamentId], references: [id])
  user             User       @relation(fields: [userId], references: [id])
  votesAsFirst     Vote[]     @relation("FirstSubmission")
  votesAsSecond    Vote[]     @relation("SecondSubmission")
}

model CoinTransaction {
  id                  String      @id @default(uuid())
  userId              String
  type                String
  amount              Int
  relatedTournamentId String?
  createdAt           DateTime    @default(now())
  relatedTournament   Tournament? @relation(fields: [relatedTournamentId], references: [id])
  user                User        @relation(fields: [userId], references: [id])
}

model Vote {
  id                 String     @id @default(uuid())
  voterId            String
  tournamentId       String
  firstSubmissionId  String
  secondSubmissionId String
  winnerSubmissionId String
  createdAt          DateTime   @default(now())
  bracketRound       Int        @default(1)
  firstSubmission    Submission @relation("FirstSubmission", fields: [firstSubmissionId], references: [id])
  secondSubmission   Submission @relation("SecondSubmission", fields: [secondSubmissionId], references: [id])
  tournament         Tournament @relation(fields: [tournamentId], references: [id])
  voter              User       @relation(fields: [voterId], references: [id])

  @@unique([voterId, firstSubmissionId, secondSubmissionId])
}

model Matchup {
  id                 String     @id @default(uuid())
  tournamentId       String
  firstSubmissionId  String
  secondSubmissionId String
  createdAt          DateTime   @default(now())
  bracketRound       Int        @default(1)
  isActive           Boolean    @default(true)
  firstSubmission    Submission @relation("FirstSubmission", fields: [firstSubmissionId], references: [id])
  secondSubmission   Submission @relation("SecondSubmission", fields: [secondSubmissionId], references: [id])
  tournament         Tournament @relation(fields: [tournamentId], references: [id])

  @@unique([firstSubmissionId, secondSubmissionId])
}
