const http = require('http');
const fs = require('fs');

// Determine service type based on command or environment
const isConsumer = process.env.SERVICE_TYPE === 'consumer' ||
                   process.argv.includes('redis:consumer') ||
                   process.env.npm_lifecycle_event === 'redis:consumer';

if (isConsumer) {
  // Health check for vote consumer
  // Check if consumer process is running by looking for a health file
  const healthFile = '/tmp/consumer-health';

  try {
    if (fs.existsSync(healthFile)) {
      const stats = fs.statSync(healthFile);
      const now = Date.now();
      const fileAge = now - stats.mtime.getTime();

      // Health file should be updated within last 60 seconds
      if (fileAge < 60000) {
        console.log('Consumer health check: OK');
        process.exit(0);
      } else {
        console.log('Consumer health check: Stale health file');
        process.exit(1);
      }
    } else {
      console.log('Consumer health check: No health file found');
      process.exit(1);
    }
  } catch (error) {
    console.log('Consumer health check error:', error.message);
    process.exit(1);
  }
} else {
  // Health check for API server
  const options = {
    host: 'localhost',
    port: process.env.PORT || 8080,
    path: '/health',
    timeout: 3000
  };

  const request = http.request(options, (res) => {
    console.log(`API health check STATUS: ${res.statusCode}`);
    if (res.statusCode === 200) {
      process.exit(0);
    } else {
      process.exit(1);
    }
  });

  request.on('error', (err) => {
    console.log('API health check ERROR:', err.message);
    process.exit(1);
  });

  request.on('timeout', () => {
    console.log('API health check TIMEOUT');
    request.destroy();
    process.exit(1);
  });

  request.setTimeout(3000);
  request.end();
}
