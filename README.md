### Docker compose for DB
- We can now run DB in a docker instead of local
#### Steps
- download docker desktop app (useful) : `https://www.docker.com/products/docker-desktop`
- Install docker to your local PC (MAC arm64)
  - `brew install docker-compose`
- Configure database connection in root `.env` file using one of these options:

**Option 1: Individual parameters (recommended for development)**
```env
DB_USER=admin
DB_PASSWORD=admin
DB_HOST=localhost
DB_PORT=5432
DB_NAME=getrankt
DB_SCHEMA=public
```

**Option 2: Complete DATABASE_URL (recommended for production)**
```env
DATABASE_URL="postgresql://admin:admin@localhost:5432/getrankt?schema=public"
```

- docker-compose up -d ( run docker as daemon)
- `npx prisma migrate dev --name init`
- `npx prisma studio`

Note: for now lets use compose as we might need more dockers and compose is good for managing multiple.

##### DB Migration (If necessary)
- `npx prisma migrate dev --name init`

