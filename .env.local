# Local Docker Deployment Environment
# Copy this file to .env.local for local Docker deployment

# ==================== APPLICATION CONFIGURATION ====================
NODE_ENV=production
PORT=8080
HOST=0.0.0.0

# ==================== DATABASE CONFIGURATION ====================
# Database credentials for local Docker deployment
DB_USER=admin
DB_PASSWORD=admin
DB_HOST=db
DB_PORT=5432
DB_NAME=getrankt
DB_SCHEMA=public

# Constructed DATABASE_URL for Prisma (dynamically built from env vars)
DATABASE_URL=postgresql://${DB_USER}:${DB_PASSWORD}@${DB_HOST}:${DB_PORT}/${DB_NAME}?schema=${DB_SCHEMA}

# Connection Pool Configuration
DB_CONNECTION_LIMIT=10
DB_POOL_TIMEOUT=20

# ==================== AUTHENTICATION CONFIGURATION ====================
# IMPORTANT: Change this to a secure random string for production-like testing
JWT_SECRET=your_super_secure_jwt_secret_for_local_docker_deployment_change_this

# ==================== REDIS CONFIGURATION ====================
REDIS_URL=redis://redis:6379
REDIS_RETRY_ATTEMPTS=3
REDIS_RETRY_DELAY=5000
REDIS_CONNECTION_TIMEOUT=10000

# ==================== LOGGING CONFIGURATION ====================
LOG_LEVEL=info

# ==================== SECURITY NOTES ====================
# 1. This is for LOCAL DOCKER deployment only
# 2. Change all passwords and secrets before using
# 3. Services are accessible on localhost only
# 4. Database: localhost:5432
# 5. API: localhost:8080
# 6. Prisma Studio: localhost:5555 (if enabled)
