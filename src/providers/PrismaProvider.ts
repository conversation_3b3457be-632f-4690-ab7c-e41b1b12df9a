import { PrismaClient } from '@prisma/client'

/**
 * PrismaProvider - Centralized Prisma client instance
 *
 * This provider ensures we have a single Prisma client instance across the application,
 * which is important for connection pooling and avoiding connection limit issues.
 */
class PrismaProvider {
  private static instance: PrismaClient | null = null

  /**
   * Construct database URL from environment variables
   */
  private static constructDatabaseUrl(): string {

    // Otherwise, construct from individual components
    const {
      DB_USER,
      DB_PASSWORD,
      DB_HOST = 'localhost',
      DB_PORT = '5432',
      DB_NAME,
      DB_SSL_MODE,
      DB_SCHEMA = 'public'
    } = process.env

    if (!DB_USER || !DB_PASSWORD || !DB_NAME) {
      throw new Error('Database configuration incomplete. Provide DB_USER, DB_PASSWORD, and DB_NAME')
    }

    // Construct the base URL
    let databaseUrl = `postgresql://${DB_USER}:${DB_PASSWORD}@${DB_HOST}:${DB_PORT}/${DB_NAME}`

    // Add query parameters
    const params = new URLSearchParams()
    params.append('schema', DB_SCHEMA)

    if (DB_SSL_MODE) {
      params.append('sslmode', DB_SSL_MODE)
    }

    // Add SSL certificate parameters if provided
    if (process.env.DB_SSL_CERT) {
      params.append('sslcert', process.env.DB_SSL_CERT)
    }
    if (process.env.DB_SSL_KEY) {
      params.append('sslkey', process.env.DB_SSL_KEY)
    }
    if (process.env.DB_SSL_ROOT_CERT) {
      params.append('sslrootcert', process.env.DB_SSL_ROOT_CERT)
    }

    // Add connection pool parameters if provided
    if (process.env.DB_CONNECTION_LIMIT) {
      params.append('connection_limit', process.env.DB_CONNECTION_LIMIT)
    }
    if (process.env.DB_POOL_TIMEOUT) {
      params.append('pool_timeout', process.env.DB_POOL_TIMEOUT)
    }

    return `${databaseUrl}?${params.toString()}`
  }

  /**
   * Get the singleton Prisma client instance
   */
  public static getInstance(): PrismaClient {
    if (!PrismaProvider.instance) {
      const databaseUrl = this.constructDatabaseUrl()

      PrismaProvider.instance = new PrismaClient({
        datasources: {
          db: {
            url: databaseUrl
          }
        },
        log: process.env.NODE_ENV === 'development' ? ['info', 'warn', 'error'] : ['error'],
      })

      // Handle graceful shutdown
      process.on('beforeExit', async () => {
        await PrismaProvider.disconnect()
      })

      process.on('SIGINT', async () => {
        await PrismaProvider.disconnect()
        process.exit(0)
      })

      process.on('SIGTERM', async () => {
        await PrismaProvider.disconnect()
        process.exit(0)
      })
    }

    return PrismaProvider.instance
  }

  /**
   * Disconnect from the database
   */
  public static async disconnect(): Promise<void> {
    if (PrismaProvider.instance) {
      await PrismaProvider.instance.$disconnect()
      PrismaProvider.instance = null
    }
  }

  /**
   * Reset the instance (useful for testing)
   */
  public static reset(): void {
    PrismaProvider.instance = null
  }
}

// Export the singleton instance getter
export const prisma = PrismaProvider.getInstance()

// Export the provider class for advanced usage
export default PrismaProvider
