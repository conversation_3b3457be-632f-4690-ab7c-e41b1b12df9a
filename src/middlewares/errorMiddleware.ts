import { Request, Response, NextFunction } from 'express'
import { Prisma } from '@prisma/client'
import {
  HttpStatusCode,
  ErrorMessage,
  ErrorType,
  PRISMA_ERROR_MAP
} from '../enums/ErrorEnums'
import { logError } from '../utils/errorLogger'

/**
 * Global error handling middleware
 * This middleware catches all unhandled errors and sanitizes them before sending to client
 */
export const globalErrorHandler = (
  error: any,
  req: Request,
  res: Response,
  _next: NextFunction
): void => {
  // Log the full error for debugging (server-side only)
  logError('Global error handler caught', error, req)

  // Determine status code and message
  let statusCode = HttpStatusCode.INTERNAL_SERVER_ERROR
  let message: string = ErrorMessage.INTERNAL_SERVER_ERROR
  let data = null

  // Handle specific error types
  if (error.statusCode) {
    statusCode = error.statusCode
    message = error.message
  } else if (error instanceof Prisma.PrismaClientKnownRequestError) {
    // Handle Prisma database errors using the error map
    const prismaError = PRISMA_ERROR_MAP[error.code]
    if (prismaError) {
      statusCode = prismaError.status
      message = prismaError.message
    } else {
      statusCode = HttpStatusCode.INTERNAL_SERVER_ERROR
      message = ErrorMessage.DATABASE_ERROR

      // In development, provide more specific error info
      if (process.env.NODE_ENV === 'development') {
        message = `Database error: ${error.code}`
      }
    }
  } else if (error instanceof Prisma.PrismaClientUnknownRequestError) {
    statusCode = HttpStatusCode.SERVICE_UNAVAILABLE
    message = ErrorMessage.DATABASE_CONNECTION_ERROR
  } else if (error instanceof Prisma.PrismaClientValidationError) {
    statusCode = HttpStatusCode.BAD_REQUEST
    message = ErrorMessage.INVALID_INPUT
  } else if (error.name === ErrorType.VALIDATION_ERROR) {
    statusCode = HttpStatusCode.BAD_REQUEST
    message = ErrorMessage.VALIDATION_FAILED
  } else if (error.name === ErrorType.AUTHENTICATION_ERROR || error.name === 'JsonWebTokenError') {
    statusCode = HttpStatusCode.UNAUTHORIZED
    message = ErrorMessage.AUTHENTICATION_FAILED
  } else if (error.name === ErrorType.AUTHORIZATION_ERROR) {
    statusCode = HttpStatusCode.FORBIDDEN
    message = ErrorMessage.FORBIDDEN
  } else if (error.name === ErrorType.NOT_FOUND_ERROR) {
    statusCode = HttpStatusCode.NOT_FOUND
    message = ErrorMessage.NOT_FOUND
  } else if (error.name === ErrorType.CONFLICT_ERROR) {
    statusCode = HttpStatusCode.CONFLICT
    message = ErrorMessage.CONFLICT
  } else if (error.name === ErrorType.RATE_LIMIT_ERROR) {
    statusCode = HttpStatusCode.TOO_MANY_REQUESTS
    message = ErrorMessage.TOO_MANY_REQUESTS
  }

  // In production, never expose internal error details
  if (process.env.NODE_ENV === 'production' && statusCode >= HttpStatusCode.INTERNAL_SERVER_ERROR) {
    message = ErrorMessage.INTERNAL_SERVER_ERROR
    data = null
  }

  // Send sanitized error response
  const response = {
    data,
    message,
    success: false,
    statusCode
  }

  res.status(statusCode).json(response)
}

/**
 * Middleware to handle 404 errors for undefined routes
 */
export const notFoundHandler = (req: Request, _res: Response, next: NextFunction): void => {
  const error = new Error(`Route not found: ${req.method} ${req.path}`)
  ;(error as any).statusCode = HttpStatusCode.NOT_FOUND
  next(error)
}

/**
 * Async error wrapper to catch errors in async route handlers
 */
export const asyncErrorHandler = (fn: Function) => {
  return (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(next)
  }
}

/**
 * Custom error classes for better error handling
 */
export class AppError extends Error {
  public statusCode: number
  public isOperational: boolean

  constructor(message: string, statusCode: number) {
    super(message)
    this.statusCode = statusCode
    this.isOperational = true

    Error.captureStackTrace(this, this.constructor)
  }
}

export class ValidationError extends AppError {
  constructor(message: string = ErrorMessage.VALIDATION_FAILED) {
    super(message, HttpStatusCode.BAD_REQUEST)
    this.name = ErrorType.VALIDATION_ERROR
  }
}

export class UnauthorizedError extends AppError {
  constructor(message: string = ErrorMessage.UNAUTHORIZED) {
    super(message, HttpStatusCode.UNAUTHORIZED)
    this.name = ErrorType.AUTHENTICATION_ERROR
  }
}

export class ForbiddenError extends AppError {
  constructor(message: string = ErrorMessage.FORBIDDEN) {
    super(message, HttpStatusCode.FORBIDDEN)
    this.name = ErrorType.AUTHORIZATION_ERROR
  }
}

export class NotFoundError extends AppError {
  constructor(message: string = ErrorMessage.NOT_FOUND) {
    super(message, HttpStatusCode.NOT_FOUND)
    this.name = ErrorType.NOT_FOUND_ERROR
  }
}

export class ConflictError extends AppError {
  constructor(message: string = ErrorMessage.CONFLICT) {
    super(message, HttpStatusCode.CONFLICT)
    this.name = ErrorType.CONFLICT_ERROR
  }
}

export class TooManyRequestsError extends AppError {
  constructor(message: string = ErrorMessage.TOO_MANY_REQUESTS) {
    super(message, HttpStatusCode.TOO_MANY_REQUESTS)
    this.name = ErrorType.RATE_LIMIT_ERROR
  }
}
