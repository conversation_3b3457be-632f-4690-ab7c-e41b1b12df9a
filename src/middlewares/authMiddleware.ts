import { Request, Response, NextFunction } from 'express'
import { verifyToken } from '../utils/auth'
import { sendError } from '../utils/responseWrapper'
import { HttpStatusCode } from '../enums/ErrorEnums'
import { logSecurityError } from '../utils/errorLogger'

export const requireAuth = (req: Request, res: Response, next: NextFunction): void => {
  const authHeader = req.headers.authorization
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    logSecurityError('Authentication failed: Missing or invalid Authorization header',
      new Error('Missing or invalid Authorization header'), req)
    sendError(res, 'Authentication required', HttpStatusCode.UNAUTHORIZED)
    return
  }

  const token = authHeader.split(' ')[1]
  try {
    const decoded = verifyToken(token) as { userId: string }
    ;(req as any).userId = decoded.userId
    next()
  } catch (err) {
    logSecurityError('Authentication failed: Invalid token', err, req)
    sendError(res, 'Invalid authentication token', HttpStatusCode.UNAUTHORIZED)
    return
  }
}
