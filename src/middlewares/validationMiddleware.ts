import { body, validationResult, Validation<PERSON>hain } from 'express-validator'
import { Request, Response, NextFunction } from 'express'
import { sendError } from '../utils/responseWrapper'
import { HttpStatusCode } from '../enums/ErrorEnums'
import { logValidationError } from '../utils/errorLogger'

/**
 * Middleware to handle validation errors
 */
export const handleValidationErrors = (req: Request, res: Response, next: NextFunction): void => {
  const errors = validationResult(req)

  if (!errors.isEmpty()) {
    const formattedErrors = errors.array().map(error => ({
      field: error.type === 'field' ? error.path : 'unknown',
      message: error.msg,
      // Remove value field to prevent data exposure in error responses
    }))

    // Log validation errors for debugging (server-side only)
    logValidationError('Validation failed', formattedErrors, req)

    sendError(res, 'Validation failed', HttpStatusCode.BAD_REQUEST, { errors: formattedErrors })
    return
  }

  next()
}

/**
 * Email validation rules
 */
export const validateEmail = (): ValidationChain => {
  return body('email')
    .trim()
    .notEmpty()
    .withMessage('Email is required')
    .isEmail()
    .withMessage('Please provide a valid email address')
    .normalizeEmail({
      gmail_remove_dots: false,
      gmail_remove_subaddress: false,
      outlookdotcom_remove_subaddress: false,
      yahoo_remove_subaddress: false,
      icloud_remove_subaddress: false
    })
    .isLength({ max: 52 })
    .withMessage('Email must not exceed 52 characters')
    .matches(/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/)
    .withMessage('Email format is invalid')
}

/**
 * Password validation rules with security requirements
 */
export const validatePassword = (): ValidationChain => {
  return body('password')
    .notEmpty()
    .withMessage('Password is required')
    .isLength({ min: 8, max: 32 })
    .withMessage('Password must be between 8 and 32 characters long')
    .custom((value: string) => {
      // Check for common weak passwords
      const commonPasswords = [
        'password', '123456', '123456789', 'qwerty', 'abc123', 
        'password123', 'admin', 'letmein', 'welcome', 'monkey'
      ]
      
      if (commonPasswords.some(common => value.toLowerCase().includes(common.toLowerCase()))) {
        throw new Error('Password contains common weak patterns')
      }
    
      return true
    })
}

/**
 * Username validation rules
 */
export const validateUsername = (): ValidationChain => {
  return body('username')
    .trim()
    .notEmpty()
    .withMessage('Username is required')
    .isLength({ min: 3, max: 20 })
    .withMessage('Username must be between 3 and 20 characters')
    .matches(/^[a-zA-Z0-9_]+$/)
    .withMessage('Username can only contain letters, numbers, and underscores')
    .custom((value: string) => {
      // Check for reserved usernames
      const reservedUsernames = [
        'admin', 'administrator', 'root', 'system', 'api', 'www', 
        'mail', 'ftp', 'support', 'help', 'info', 'contact',
        'null', 'undefined', 'demo'
      ]
      
      if (reservedUsernames.includes(value.toLowerCase())) {
        throw new Error('Username is reserved and cannot be used')
      }
      
      return true
    })
}

/**
 * Login validation rules (less strict than signup)
 */
export const validateLoginEmail = (): ValidationChain => {
  return body('email')
    .trim()
    .notEmpty()
    .withMessage('Email is required')
    .isEmail()
    .withMessage('Please provide a valid email address')
    .normalizeEmail({
      gmail_remove_dots: false,
      gmail_remove_subaddress: false,
      outlookdotcom_remove_subaddress: false,
      yahoo_remove_subaddress: false,
      icloud_remove_subaddress: false
    })
}

export const validateLoginPassword = (): ValidationChain => {
  return body('password')
    .notEmpty()
    .withMessage('Password is required')
    .isLength({ min: 8, max: 32 })
    .withMessage('Password is required')
}

/**
 * Validation rule sets for different endpoints
 */
export const signupValidation = [
  validateEmail(),
  validatePassword(),
  validateUsername(),
  handleValidationErrors
]

export const loginValidation = [
  validateLoginEmail(),
  validateLoginPassword(),
  handleValidationErrors
]
