import rateLimit from 'express-rate-limit'
import { Request, Response } from 'express'

/**
 * Rate limiting configuration for authentication endpoints
 */

// General auth rate limiting - applies to all auth endpoints
export const authRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 40, // Limit each IP to 40 requests per windowMs
  message: {
    success: false,
    message: 'Too many authentication attempts. Please try again in 15 minutes.',
    statusCode: 429
  },
  standardHeaders: true, // Return rate limit info in the `RateLimit-*` headers
  legacyHeaders: false, // Disable the `X-RateLimit-*` headers
  handler: (req: Request, res: Response) => {
    console.log(`Rate limit exceeded for IP: ${req.ip} on ${req.path}`)
    res.status(429).json({
      success: false,
      message: 'Too many authentication attempts. Please try again in 15 minutes.',
      statusCode: 429,
      data: null
    })
  }
})

// Stricter rate limiting for login attempts (brute force protection)
export const loginRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5, // Limit each IP to 5 login attempts per windowMs
  message: {
    success: false,
    message: 'Too many login attempts. Please try again in 15 minutes.',
    statusCode: 429
  },
  standardHeaders: true,
  legacyHeaders: false,
  skipSuccessfulRequests: true, // Don't count successful requests
  handler: (req: Request, res: Response) => {
    console.log(`Login rate limit exceeded for IP: ${req.ip}, email: ${req.body?.email || 'unknown'}`)
    res.status(429).json({
      success: false,
      message: 'Too many login attempts. Please try again in 15 minutes.',
      statusCode: 429,
      data: null
    })
  }
})

// Rate limiting for signup attempts
export const signupRateLimit = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 3, // Limit each IP to 3 signup attempts per hour
  message: {
    success: false,
    message: 'Too many signup attempts. Please try again in 1 hour.',
    statusCode: 429
  },
  standardHeaders: true,
  legacyHeaders: false,
  handler: (req: Request, res: Response) => {
    console.log(`Signup rate limit exceeded for IP: ${req.ip}, email: ${req.body?.email || 'unknown'}`)
    res.status(429).json({
      success: false,
      message: 'Too many signup attempts. Please try again in 1 hour.',
      statusCode: 429,
      data: null
    })
  }
})

// Rate limiting for password reset requests
export const passwordResetRateLimit = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 3, // Limit each IP to 3 password reset attempts per hour
  message: {
    success: false,
    message: 'Too many password reset attempts. Please try again in 1 hour.',
    statusCode: 429
  },
  standardHeaders: true,
  legacyHeaders: false,
  handler: (req: Request, res: Response) => {
    console.log(`Password reset rate limit exceeded for IP: ${req.ip}`)
    res.status(429).json({
      success: false,
      message: 'Too many password reset attempts. Please try again in 1 hour.',
      statusCode: 429,
      data: null
    })
  }
})
