import { Request, Response, NextFunction } from 'express'
import { body } from 'express-validator'
import { HttpStatusCode } from '../enums/ErrorEnums'
import { logSecurityError } from '../utils/errorLogger'

/**
 * Input sanitization middleware to prevent injection attacks
 */
export const sanitizeInput = (req: Request, res: Response, next: NextFunction): void => {
  // Sanitize common injection patterns
  const sanitizeString = (str: string): string => {
    if (typeof str !== 'string') return str
    
    return str
      // Remove potential SQL injection patterns
      .replace(/['"`;\\]/g, '')
      // Remove potential NoSQL injection patterns
      .replace(/[${}]/g, '')
      // Remove potential XSS patterns
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
      .replace(/javascript:/gi, '')
      .replace(/on\w+\s*=/gi, '')
      // Trim whitespace
      .trim()
  }

  // Recursively sanitize object properties
  const sanitizeObject = (obj: any): any => {
    if (obj === null || obj === undefined) return obj
    
    if (typeof obj === 'string') {
      return sanitizeString(obj)
    }
    
    if (Array.isArray(obj)) {
      return obj.map(sanitizeObject)
    }
    
    if (typeof obj === 'object') {
      const sanitized: any = {}
      for (const key in obj) {
        if (obj.hasOwnProperty(key)) {
          sanitized[key] = sanitizeObject(obj[key])
        }
      }
      return sanitized
    }
    
    return obj
  }

  // Sanitize request body
  if (req.body) {
    req.body = sanitizeObject(req.body)
  }

  // Sanitize query parameters (modify properties, don't reassign)
  if (req.query) {
    for (const key in req.query) {
      if (req.query.hasOwnProperty(key)) {
        (req.query as any)[key] = sanitizeObject(req.query[key])
      }
    }
  }

  // Sanitize URL parameters (modify properties, don't reassign)
  if (req.params) {
    for (const key in req.params) {
      if (req.params.hasOwnProperty(key)) {
        (req.params as any)[key] = sanitizeObject(req.params[key])
      }
    }
  }

  next()
}

/**
 * API security headers middleware (optimized for React Native mobile app)
 */
export const securityHeaders = (req: Request, res: Response, next: NextFunction): void => {
  // Prevent MIME type sniffing - still relevant for API responses
  res.setHeader('X-Content-Type-Options', 'nosniff')

  // API-specific security headers
  res.setHeader('X-API-Version', '1.0')

  // Prevent caching of sensitive API responses
  res.setHeader('Cache-Control', 'no-store, no-cache, must-revalidate, private')
  res.setHeader('Pragma', 'no-cache')
  res.setHeader('Expires', '0')

  // Remove server information disclosure
  res.removeHeader('X-Powered-By')

  next()
}

/**
 * Request size limiting middleware with configurable limits
 */
export const createSizeLimitMiddleware = (maxSizeMB: number) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    const contentLength = req.get('content-length')
    const maxSize = maxSizeMB * 1024 * 1024 // Convert MB to bytes

    if (contentLength && parseInt(contentLength) > maxSize) {
      logSecurityError('Request size limit exceeded',
        new Error(`Request size limit exceeded: ${contentLength} bytes (max: ${maxSize})`),
        req, { contentLength, maxSize })
      res.status(HttpStatusCode.REQUEST_ENTITY_TOO_LARGE).json({
        success: false,
        message: `Request entity too large. Maximum size allowed: ${maxSizeMB}MB`,
        statusCode: HttpStatusCode.REQUEST_ENTITY_TOO_LARGE,
        data: null
      })
      return
    }

    next()
  }
}

// Default size limit for regular API endpoints
export const limitRequestSize = createSizeLimitMiddleware(1) // 1MB

// Size limit for video uploads
export const limitVideoUploadSize = createSizeLimitMiddleware(100) // 100MB

// Size limit for image uploads
export const limitImageUploadSize = createSizeLimitMiddleware(10) // 10MB

/**
 * Suspicious activity detection middleware
 */
export const detectSuspiciousActivity = (req: Request, res: Response, next: NextFunction): void => {
  const suspiciousPatterns = [
    // SQL injection patterns
    /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\b)/i,
    // NoSQL injection patterns
    /(\$where|\$ne|\$gt|\$lt|\$regex)/i,
    // XSS patterns
    /<script|javascript:|on\w+\s*=/i,
    // Path traversal
    /\.\.[\/\\]/,
    // Command injection
    /[;&|`$()]/
  ]

  const checkForSuspiciousContent = (obj: any): boolean => {
    if (typeof obj === 'string') {
      return suspiciousPatterns.some(pattern => pattern.test(obj))
    }
    
    if (Array.isArray(obj)) {
      return obj.some(checkForSuspiciousContent)
    }
    
    if (typeof obj === 'object' && obj !== null) {
      return Object.values(obj).some(checkForSuspiciousContent)
    }
    
    return false
  }

  // Check request body, query, and params for suspicious content
  const isSuspicious = 
    checkForSuspiciousContent(req.body) ||
    checkForSuspiciousContent(req.query) ||
    checkForSuspiciousContent(req.params)

  if (isSuspicious) {
    logSecurityError('Suspicious activity detected',
      new Error('Invalid request content detected'), req, {
        suspiciousContentDetected: true
      })

    res.status(HttpStatusCode.BAD_REQUEST).json({
      success: false,
      message: 'Invalid request content detected',
      statusCode: HttpStatusCode.BAD_REQUEST,
      data: null
    })
    return
  }

  next()
}

/**
 * Email domain validation (optional - can be used to block disposable emails)
 */
export const validateEmailDomain = () => {
  return body('email').custom(async (email: string) => {
    if (!email) return true // Let other validators handle empty email
    
    const domain = email.split('@')[1]?.toLowerCase()
    
    // List of commonly blocked disposable email domains
    const disposableEmailDomains = [
      '10minutemail.com', 'tempmail.org', 'guerrillamail.com',
      'mailinator.com', 'yopmail.com', 'temp-mail.org'
    ]
    
    if (disposableEmailDomains.includes(domain)) {
      throw new Error('Disposable email addresses are not allowed')
    }
    
    return true
  })
}
