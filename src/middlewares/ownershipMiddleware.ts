import { Request, Response, NextFunction } from 'express'
import { sendError } from '../utils/responseWrapper'
import { HttpStatusCode } from '../enums/ErrorEnums'
import { logSecurityError } from '../utils/errorLogger'
import { DatabaseOperations } from '../database/DatabaseOperations'

/**
 * Middleware to validate tournament ownership
 * Ensures only the tournament creator can modify/delete tournaments
 */
export const validateTournamentOwnership = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  const tournamentId = req.params.id
  const userId = (req as any).userId

  try {
    if (!tournamentId) {
      sendError(res, 'Tournament ID is required', HttpStatusCode.BAD_REQUEST)
      return
    }

    const tournament = await DatabaseOperations.findTournamentById(tournamentId)

    if (!tournament) {
      sendError(res, 'Tournament not found', HttpStatusCode.NOT_FOUND)
      return
    }

    if (tournament.createdById !== userId) {
      logSecurityError('Unauthorized tournament access attempt', 
        new Error('User attempted to access tournament they do not own'), req, 
        { userId, tournamentId, tournamentOwnerId: tournament.createdById })
      sendError(res, 'Access denied. You can only modify tournaments you created.', HttpStatusCode.FORBIDDEN)
      return
    }

    // Store tournament in request for use in controller
    ;(req as any).tournament = tournament
    next()
  } catch (err) {
    logSecurityError('Tournament ownership validation failed', err, req, { userId, tournamentId })
    sendError(res, 'Failed to validate tournament ownership', HttpStatusCode.INTERNAL_SERVER_ERROR)
    return
  }
}
