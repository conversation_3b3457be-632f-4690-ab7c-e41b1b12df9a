import { redisManager } from './RedisConfig'
import { logError, logInfo } from '../utils/errorLogger'
import * as fs from 'fs'

export class VoteConsumer {
  private isRunning: boolean = false
  private shutdownPromise: Promise<void> | null = null
  private healthInterval: NodeJS.Timeout | null = null

  /**
   * Start the vote consumer service
   */
  async start(): Promise<void> {
    if (this.isRunning) {
      logInfo('Vote consumer start skipped', 'Consumer already running', undefined, {
        operationType: 'consumer_already_running'
      })
      return
    }

    try {
      logInfo('Vote consumer starting', 'Initializing vote consumer service', undefined, {
        operationType: 'consumer_start_init'
      })

      // Initialize Redis voting stream if not already done
      await redisManager.initializeVotingStream()

      // Start the consumer
      await redisManager.startVoteConsumer()

      this.isRunning = true

      // Start health file updates
      this.startHealthMonitoring()

      logInfo('Vote consumer started', 'Vote consumer service initialized successfully', undefined, {
        operationType: 'consumer_start_success'
      })

    } catch (error) {
      logError('Vote consumer start failed', error, undefined, {
        operationType: 'consumer_start_error',
        errorType: error instanceof Error ? error.constructor.name : 'unknown'
      })
      throw error
    }
  }

  /**
   * Stop the vote consumer service
   */
  async stop(): Promise<void> {
    if (!this.isRunning) {
      logInfo('Vote consumer stop skipped', 'Consumer not running', undefined, {
        operationType: 'consumer_not_running'
      })
      return
    }

    logInfo('Vote consumer stopping', 'Stopping vote consumer service', undefined, {
      operationType: 'consumer_stop_init'
    })

    // Stop the consumer
    redisManager.stopVoteConsumer()

    // Stop health monitoring
    this.stopHealthMonitoring()

    this.isRunning = false
    logInfo('Vote consumer stopped', 'Vote consumer service stopped successfully', undefined, {
      operationType: 'consumer_stop_success'
    })
  }

  /**
   * Start health file monitoring for container health checks
   */
  private startHealthMonitoring(): void {
    const healthFile = '/tmp/consumer-health'

    // Update health file immediately
    this.updateHealthFile(healthFile)

    // Update health file every 30 seconds
    this.healthInterval = setInterval(() => {
      this.updateHealthFile(healthFile)
    }, 30000)

    logInfo('Health monitoring started', 'Consumer health monitoring initialized', undefined, {
      operationType: 'health_monitoring_start'
    })
  }

  /**
   * Stop health file monitoring
   */
  private stopHealthMonitoring(): void {
    if (this.healthInterval) {
      clearInterval(this.healthInterval)
      this.healthInterval = null

      // Clean up health file
      try {
        fs.unlinkSync('/tmp/consumer-health')
      } catch (error) {
        // Ignore cleanup errors
      }

      logInfo('Health monitoring stopped', 'Consumer health monitoring stopped', undefined, {
        operationType: 'health_monitoring_stop'
      })
    }
  }

  /**
   * Update health file with current timestamp
   */
  private updateHealthFile(healthFile: string): void {
    try {
      fs.writeFileSync(healthFile, new Date().toISOString())
    } catch (error) {
      logError('Health file update failed', error, undefined, {
        operationType: 'health_file_error',
        errorType: error instanceof Error ? error.constructor.name : 'unknown'
      })
    }
  }

  /**
   * Graceful shutdown with timeout
   */
  async gracefulShutdown(timeoutMs: number = 30000): Promise<void> {
    if (this.shutdownPromise) {
      return this.shutdownPromise
    }

    this.shutdownPromise = new Promise(async (resolve) => {
      logInfo('Graceful shutdown initiated', 'Starting graceful shutdown process', undefined, {
        operationType: 'graceful_shutdown_init'
      })

      const shutdownTimeout = setTimeout(() => {
        logInfo('Shutdown timeout reached', 'Forcing shutdown due to timeout', undefined, {
          operationType: 'graceful_shutdown_timeout'
        })
        resolve()
      }, timeoutMs)

      try {
        await this.stop()
        await redisManager.shutdown()
        clearTimeout(shutdownTimeout)
        logInfo('Graceful shutdown completed', 'Shutdown process completed successfully', undefined, {
          operationType: 'graceful_shutdown_success'
        })
        resolve()
      } catch (error) {
        logError('Graceful shutdown error', error, undefined, {
          operationType: 'graceful_shutdown_error',
          errorType: error instanceof Error ? error.constructor.name : 'unknown'
        })
        clearTimeout(shutdownTimeout)
        resolve()
      }
    })

    return this.shutdownPromise
  }

  /**
   * Check if consumer is running
   */
  isConsumerRunning(): boolean {
    return this.isRunning
  }

  /**
   * Get consumer status and metrics
   */
  async getStatus(): Promise<{
    isRunning: boolean
    redisHealth: any
    queueMetrics: any
  }> {
    try {
      const redisHealth = await redisManager.healthCheck()
      
      let queueMetrics = {
        streamLength: 0,
        pendingMessages: 0,
        consumerGroups: 0
      }

      if (redisHealth.status === 'healthy') {
        try {
          const votingStream = redisManager.getVotingStream()
          const streamInfo = await votingStream.getStreamInfo()
          queueMetrics = {
            streamLength: streamInfo.length || 0,
            pendingMessages: streamInfo['pending-messages'] || 0,
            consumerGroups: streamInfo.groups || 0
          }
        } catch (error) {
          logError('Queue metrics fetch failed', error, undefined, {
            operationType: 'queue_metrics_error',
            errorType: error instanceof Error ? error.constructor.name : 'unknown'
          })
        }
      }

      return {
        isRunning: this.isRunning,
        redisHealth,
        queueMetrics
      }
    } catch (error) {
      logError('Consumer status check failed', error, undefined, {
        operationType: 'consumer_status_error',
        errorType: error instanceof Error ? error.constructor.name : 'unknown'
      })
      return {
        isRunning: this.isRunning,
        redisHealth: { status: 'unhealthy', details: { error: 'Failed to check health' } },
        queueMetrics: { streamLength: 0, pendingMessages: 0, consumerGroups: 0 }
      }
    }
  }
}

// Export singleton instance
export const voteConsumer = new VoteConsumer()

// Handle process signals for graceful shutdown
process.on('SIGTERM', async () => {
  logInfo('Process signal received', 'SIGTERM signal received', undefined, {
    operationType: 'process_sigterm'
  })
  await voteConsumer.gracefulShutdown()
  process.exit(0)
})

process.on('SIGINT', async () => {
  logInfo('Process signal received', 'SIGINT signal received', undefined, {
    operationType: 'process_sigint'
  })
  await voteConsumer.gracefulShutdown()
  process.exit(0)
})

// Handle uncaught exceptions
process.on('uncaughtException', async (error) => {
  logError('Uncaught exception', error, undefined, {
    operationType: 'uncaught_exception',
    errorType: error instanceof Error ? error.constructor.name : 'unknown'
  })
  await voteConsumer.gracefulShutdown(5000) // Shorter timeout for emergency shutdown
  process.exit(1)
})

process.on('unhandledRejection', async (reason, _promise) => {
  logError('Unhandled rejection', reason instanceof Error ? reason : new Error(String(reason)), undefined, {
    operationType: 'unhandled_rejection',
    errorType: reason instanceof Error ? reason.constructor.name : 'unknown'
  })
  await voteConsumer.gracefulShutdown(5000) // Shorter timeout for emergency shutdown
  process.exit(1)
})
