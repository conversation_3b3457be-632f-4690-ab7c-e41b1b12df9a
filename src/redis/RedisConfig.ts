import { RedisVotingStream } from './RedisVotingStream'
import { logError, logInfo } from '../utils/errorLogger'

export interface RedisConfiguration {
  url: string
  retryAttempts: number
  retryDelay: number
  connectionTimeout: number
}

export class RedisManager {
  private static instance: RedisManager
  private votingStream: RedisVotingStream | null = null
  private config: RedisConfiguration

  private constructor() {
    this.config = {
      url: process.env.REDIS_URL || 'redis://localhost:6379',
      retryAttempts: parseInt(process.env.REDIS_RETRY_ATTEMPTS || '3'),
      retryDelay: parseInt(process.env.REDIS_RETRY_DELAY || '5000'),
      connectionTimeout: parseInt(process.env.REDIS_CONNECTION_TIMEOUT || '10000')
    }
  }

  /**
   * Get singleton instance
   */
  public static getInstance(): RedisManager {
    if (!RedisManager.instance) {
      RedisManager.instance = new RedisManager()
    }
    return RedisManager.instance
  }

  /**
   * Initialize Redis voting stream
   */
  public async initializeVotingStream(): Promise<RedisVotingStream> {
    if (this.votingStream) {
      return this.votingStream
    }

    logInfo('Redis stream init', 'Initializing Redis voting stream', undefined, {
      operationType: 'redis_stream_init'
    })

    this.votingStream = new RedisVotingStream()

    let attempts = 0
    while (attempts < this.config.retryAttempts) {
      try {
        await this.votingStream.initialize()
        logInfo('Redis stream init success', 'Redis voting stream initialized successfully', undefined, {
          operationType: 'redis_stream_init_success'
        })
        return this.votingStream
      } catch (error) {
        attempts++
        logError('Redis stream init failed', error, undefined, {
          operationType: 'redis_stream_init_failed',
          attempt: attempts,
          maxAttempts: this.config.retryAttempts,
          errorType: error instanceof Error ? error.constructor.name : 'unknown'
        })

        if (attempts < this.config.retryAttempts) {
          logInfo('Redis stream retry', `Retrying in ${this.config.retryDelay}ms`, undefined, {
            operationType: 'redis_stream_retry',
            attempt: attempts,
            retryDelay: this.config.retryDelay
          })
          await new Promise(resolve => setTimeout(resolve, this.config.retryDelay))
        } else {
          logError('Redis stream max retries', new Error('Max retry attempts reached'), undefined, {
            operationType: 'redis_stream_max_retries',
            maxAttempts: this.config.retryAttempts
          })
          throw error
        }
      }
    }

    throw new Error('Failed to initialize Redis voting stream after maximum retry attempts')
  }

  /**
   * Get voting stream instance
   */
  public getVotingStream(): RedisVotingStream {
    if (!this.votingStream) {
      throw new Error('Redis voting stream not initialized. Call initializeVotingStream() first.')
    }
    return this.votingStream
  }

  /**
   * Start the vote consumer
   */
  public async startVoteConsumer(): Promise<void> {
    const votingStream = this.getVotingStream()
    logInfo('Vote consumer start', 'Starting vote consumer', undefined, {
      operationType: 'vote_consumer_start'
    })

    // Start consumer in background
    votingStream.startConsumer().catch(error => {
      logError('Vote consumer error', error, undefined, {
        operationType: 'vote_consumer_error',
        errorType: error instanceof Error ? error.constructor.name : 'unknown'
      })
    })
  }

  /**
   * Stop the vote consumer
   */
  public stopVoteConsumer(): void {
    if (this.votingStream) {
      logInfo('Vote consumer stop', 'Stopping vote consumer', undefined, {
        operationType: 'vote_consumer_stop'
      })
      this.votingStream.stopConsumer()
    }
  }

  /**
   * Graceful shutdown
   */
  public async shutdown(): Promise<void> {
    logInfo('Redis manager shutdown', 'Shutting down Redis manager', undefined, {
      operationType: 'redis_manager_shutdown'
    })

    if (this.votingStream) {
      await this.votingStream.disconnect()
      this.votingStream = null
    }

    logInfo('Redis manager shutdown complete', 'Redis manager shutdown complete', undefined, {
      operationType: 'redis_manager_shutdown_complete'
    })
  }

  /**
   * Health check for Redis connection
   */
  public async healthCheck(): Promise<{ status: string; details: any }> {
    try {
      if (!this.votingStream) {
        return {
          status: 'unhealthy',
          details: { error: 'Voting stream not initialized' }
        }
      }

      const streamInfo = await this.votingStream.getStreamInfo()
      
      return {
        status: 'healthy',
        details: {
          streamLength: streamInfo.length,
          consumerGroups: streamInfo.groups,
          lastGeneratedId: streamInfo['last-generated-id']
        }
      }
    } catch (error) {
      return {
        status: 'unhealthy',
        details: { error: error instanceof Error ? error.message : 'Unknown error' }
      }
    }
  }
}

// Export singleton instance
export const redisManager = RedisManager.getInstance()
