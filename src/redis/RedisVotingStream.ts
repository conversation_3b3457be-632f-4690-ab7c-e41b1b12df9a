import { createClient, RedisClientType } from 'redis'
import { DatabaseOperations } from '../database/DatabaseOperations'
import { checkAndAdvanceContestants, deactivateMatchupsForQualifiedContestants } from '../services/singleEliminationService'
import { VOTES_TO_ADVANCE, TOURNAMENT_STATUS, SUBMISSION_STATUS } from '../constants/tournamentConstants'
import { logError, logInfo } from '../utils/errorLogger'

export interface VoteRequest {
  voterId: string
  tournamentId: string
  firstSubmissionId: string
  secondSubmissionId: string
  winnerSubmissionId: string
  timestamp: number
  requestId: string
}

export interface VoteResult {
  success: boolean
  message?: string
  winnerVotes?: number
  loserVotes?: number
  advancementTriggered?: boolean
  tournamentCompleted?: boolean
  error?: string
}

export class RedisVotingStream {
  private client: RedisClientType
  private isConnected: boolean = false
  private consumerRunning: boolean = false
  private readonly streamName = 'voting:stream'
  private readonly consumerGroup = 'voting:processors'
  private readonly consumerName = `processor:${process.pid}`

  constructor() {
    this.client = createClient({
      url: process.env.REDIS_URL || 'redis://localhost:6379'
    })

    this.client.on('error', (err) => {
      logError('Redis client error', err, undefined, {
        operationType: 'redis_client_error',
        errorType: err instanceof Error ? err.constructor.name : 'unknown'
      })
    })

    this.client.on('connect', () => {
      logInfo('Redis client connected', 'Redis connection established', undefined, {
        operationType: 'redis_client_connect'
      })
      this.isConnected = true
    })

    this.client.on('disconnect', () => {
      logInfo('Redis client disconnected', 'Redis connection lost', undefined, {
        operationType: 'redis_client_disconnect'
      })
      this.isConnected = false
    })
  }

  /**
   * Initialize Redis connection and consumer group
   */
  async initialize(): Promise<void> {
    try {
      await this.client.connect()
      
      // Create consumer group if it doesn't exist
      try {
        await this.client.xGroupCreate(this.streamName, this.consumerGroup, '0', {
          MKSTREAM: true
        })
        logInfo('Consumer group created', 'Redis consumer group initialized', undefined, {
          operationType: 'consumer_group_created'
        })
      } catch (error: any) {
        if (error.message.includes('BUSYGROUP')) {
          logInfo('Consumer group exists', 'Redis consumer group already exists', undefined, {
            operationType: 'consumer_group_exists'
          })
        } else {
          throw error
        }
      }
    } catch (error) {
      logError('Redis voting stream initialization failed', error, undefined, {
        operationType: 'redis_stream_init_error',
        errorType: error instanceof Error ? error.constructor.name : 'unknown'
      })
      throw error
    }
  }

  /**
   * Add a vote request to the stream (Producer)
   */
  async addVoteToStream(voteRequest: VoteRequest): Promise<string> {
    if (!this.isConnected) {
      throw new Error('Redis client is not connected')
    }

    try {
      const messageId = await this.client.xAdd(this.streamName, '*', {
        voterId: voteRequest.voterId,
        tournamentId: voteRequest.tournamentId,
        firstSubmissionId: voteRequest.firstSubmissionId,
        secondSubmissionId: voteRequest.secondSubmissionId,
        winnerSubmissionId: voteRequest.winnerSubmissionId,
        timestamp: voteRequest.timestamp.toString(),
        requestId: voteRequest.requestId
      })

      return messageId
    } catch (error) {
      logError('Failed to add vote to stream', error, undefined, {
        operationType: 'vote_stream_add_error',
        errorType: error instanceof Error ? error.constructor.name : 'unknown'
      })
      throw error
    }
  }

  /**
   * Start consuming votes from the stream (Consumer)
   */
  async startConsumer(): Promise<void> {
    if (this.consumerRunning) {
      logInfo('Consumer start skipped', 'Vote consumer already running', undefined, {
        operationType: 'consumer_already_running'
      })
      return
    }

    this.consumerRunning = true
    logInfo('Vote consumer started', 'Redis vote consumer service initialized', undefined, {
      operationType: 'consumer_start'
    })

    while (this.consumerRunning) {
      try {
        // Read messages from the stream
        const messages = await this.client.xReadGroup(
          this.consumerGroup,
          this.consumerName,
          [
            {
              key: this.streamName,
              id: '>'
            }
          ],
          {
            COUNT: 10,
            BLOCK: 1000 // Block for 1 second if no messages
          }
        )

        if (messages && messages.length > 0) {
          for (const stream of messages) {
            for (const message of stream.messages) {
              await this.processVoteMessage(message.id, message.message)
            }
          }
        }
      } catch (error) {
        logError('Vote consumer error', error, undefined, {
          operationType: 'vote_consumer_error',
          errorType: error instanceof Error ? error.constructor.name : 'unknown'
        })
        // Wait a bit before retrying
        await new Promise(resolve => setTimeout(resolve, 5000))
      }
    }
  }

  /**
   * Stop the consumer
   */
  stopConsumer(): void {
    logInfo('Vote consumer stopped', 'Stopping Redis vote consumer', undefined, {
      operationType: 'consumer_stop'
    })
    this.consumerRunning = false
  }

  /**
   * Process a single vote message from the stream
   */
  private async processVoteMessage(messageId: string, messageData: any): Promise<void> {
    try {
      const voteRequest: VoteRequest = {
        voterId: messageData.voterId,
        tournamentId: messageData.tournamentId,
        firstSubmissionId: messageData.firstSubmissionId,
        secondSubmissionId: messageData.secondSubmissionId,
        winnerSubmissionId: messageData.winnerSubmissionId,
        timestamp: parseInt(messageData.timestamp),
        requestId: messageData.requestId
      }

      // Process the vote
      const result = await this.processVote(voteRequest)

      if (result.success) {
        logInfo('Vote processed successfully', 'Vote processing completed', undefined, {
          operationType: 'vote_processing_success'
        })
      } else {
        logError('Vote processing failed', new Error(result.error || 'Unknown processing error'), undefined, {
          operationType: 'vote_processing_failed'
        })
      }

      // Acknowledge the message
      await this.client.xAck(this.streamName, this.consumerGroup, messageId)

    } catch (error) {
      logError('Vote message processing failed', error, undefined, {
        operationType: 'vote_message_processing_error',
        errorType: error instanceof Error ? error.constructor.name : 'unknown'
      })
      // In a production system, you might want to move failed messages to a dead letter queue
    }
  }

  /**
   * Process the actual vote (similar to the original submitVote logic)
   */
  private async processVote(voteRequest: VoteRequest): Promise<VoteResult> {
    try {
      // Validate inputs
      if (voteRequest.winnerSubmissionId !== voteRequest.firstSubmissionId && 
          voteRequest.winnerSubmissionId !== voteRequest.secondSubmissionId) {
        return {
          success: false,
          error: 'Winner must be one of the two contestants in the matchup'
        }
      }

      // Check if user already voted on this matchup
      const alreadyVoted = await DatabaseOperations.hasUserVotedOnMatchup(
        voteRequest.voterId, 
        voteRequest.firstSubmissionId, 
        voteRequest.secondSubmissionId
      )
      
      if (alreadyVoted) {
        return {
          success: false,
          error: 'User has already voted on this matchup'
        }
      }

      // Get tournament and verify it's active
      const tournament = await DatabaseOperations.findTournamentById(voteRequest.tournamentId)

      if (!tournament) {
        return {
          success: false,
          error: 'Tournament not found'
        }
      }

      if (tournament.status !== TOURNAMENT_STATUS.ACTIVE) {
        return {
          success: false,
          error: 'Tournament is not currently active for voting'
        }
      }

      // Get both submissions to verify they exist and are active
      const [firstSubmission, secondSubmission] = await Promise.all([
        DatabaseOperations.findSubmissionByIdWithUser(voteRequest.firstSubmissionId),
        DatabaseOperations.findSubmissionByIdWithUser(voteRequest.secondSubmissionId)
      ])

      if (!firstSubmission || !secondSubmission) {
        return {
          success: false,
          error: 'One or both contestants not found'
        }
      }

      if (firstSubmission.tournamentId !== voteRequest.tournamentId || 
          secondSubmission.tournamentId !== voteRequest.tournamentId) {
        return {
          success: false,
          error: 'Contestants must be from the specified tournament'
        }
      }

      if ((firstSubmission as any).status !== SUBMISSION_STATUS.ACTIVE || 
          (secondSubmission as any).status !== SUBMISSION_STATUS.ACTIVE) {
        return {
          success: false,
          error: 'One or both contestants are no longer active in the tournament'
        }
      }

      // Verify contestants are in the same bracket
      if ((firstSubmission as any).currentBracket !== (secondSubmission as any).currentBracket) {
        return {
          success: false,
          error: 'Contestants must be in the same bracket'
        }
      }

      // Check if user is trying to vote on their own submission
      if (firstSubmission.userId === voteRequest.voterId || secondSubmission.userId === voteRequest.voterId) {
        return {
          success: false,
          error: 'User cannot vote on matchups involving their own submission'
        }
      }

      let advancementTriggered = false
      let tournamentCompleted = false
      let winnerVotes = 0
      let loserVotes = 0

      // Process the vote in a transaction
      const voteResult = await DatabaseOperations.createVoteWithUpdate(
        voteRequest.voterId,
        voteRequest.tournamentId,
        voteRequest.firstSubmissionId,
        voteRequest.secondSubmissionId,
        voteRequest.winnerSubmissionId,
        (firstSubmission as any).currentBracket
      )

      winnerVotes = voteResult.updatedWinner.votes
      loserVotes = voteResult.updatedLoser?.votes || 0

      // Check if any contestants should advance
      const currentBracket = (firstSubmission as any).currentBracket
      const requiredVotes = VOTES_TO_ADVANCE * currentBracket

      if (winnerVotes >= requiredVotes) {
        // Deactivate matchups involving contestants who have reached promotion threshold
        const deactivatedCount = await deactivateMatchupsForQualifiedContestants(voteRequest.tournamentId, currentBracket)
        if (deactivatedCount > 0) {
          logInfo('Matchups deactivated', 'Deactivated matchups for qualified contestants', undefined, {
            operationType: 'matchups_deactivated',
            deactivatedCount
          })
        }

        const advancementResult = await checkAndAdvanceContestants(voteRequest.tournamentId)
        advancementTriggered = advancementResult.advancedContestants.length > 0
        tournamentCompleted = advancementResult.tournamentCompleted
      }

      return {
        success: true,
        message: `Vote recorded successfully. ${firstSubmission.user.username} vs ${secondSubmission.user.username}`,
        winnerVotes,
        loserVotes,
        advancementTriggered,
        tournamentCompleted
      }

    } catch (error) {
      logError('Vote processing error', error, undefined, {
        operationType: 'vote_processing_error',
        errorType: error instanceof Error ? error.constructor.name : 'unknown'
      })
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      }
    }
  }

  /**
   * Disconnect from Redis
   */
  async disconnect(): Promise<void> {
    this.stopConsumer()
    if (this.isConnected) {
      await this.client.quit()
    }
  }

  /**
   * Get stream info for monitoring
   */
  async getStreamInfo(): Promise<any> {
    if (!this.isConnected) {
      throw new Error('Redis client is not connected')
    }

    try {
      const info = await this.client.xInfoStream(this.streamName)
      return info
    } catch (error) {
      logError('Stream info fetch failed', error, undefined, {
        operationType: 'stream_info_error',
        errorType: error instanceof Error ? error.constructor.name : 'unknown'
      })
      throw error
    }
  }
}
