import { v4 as uuidv4 } from 'uuid'
import { redisManager } from './RedisConfig'
import { VoteRequest } from './RedisVotingStream'
import { DatabaseOperations } from '../database/DatabaseOperations'
import { TOURNAMENT_STATUS, SUBMISSION_STATUS } from '../constants/tournamentConstants'
import { logError, logInfo } from '../utils/errorLogger'

export interface VoteSubmissionRequest {
  voterId: string
  tournamentId: string
  firstSubmissionId: string
  secondSubmissionId: string
  winnerSubmissionId: string
}

export interface VoteSubmissionResponse {
  success: boolean
  message: string
  requestId: string
  error?: string
}

export class VoteProducer {
  /**
   * Submit a vote request to the Redis stream for asynchronous processing
   */
  static async submitVoteRequest(request: VoteSubmissionRequest): Promise<VoteSubmissionResponse> {
    // Generate unique request ID for tracking
    const requestId = uuidv4()

    try {
      
      // Perform basic validation before adding to stream
      const validationResult = await this.performBasicValidation(request)
      if (!validationResult.isValid) {
        return {
          success: false,
          message: validationResult.error!,
          requestId: requestId,
          error: validationResult.error
        }
      }

      // Create vote request object
      const voteRequest: VoteRequest = {
        voterId: request.voterId,
        tournamentId: request.tournamentId,
        firstSubmissionId: request.firstSubmissionId,
        secondSubmissionId: request.secondSubmissionId,
        winnerSubmissionId: request.winnerSubmissionId,
        timestamp: Date.now(),
        requestId: requestId
      }

      // Add to Redis stream
      const votingStream = redisManager.getVotingStream()
      const messageId = await votingStream.addVoteToStream(voteRequest)

      logInfo('Vote request queued', 'Vote processing initiated', undefined, {
        operationType: 'vote_queued',
        hasRequestId: !!requestId
      })

      return {
        success: true,
        message: 'Vote request submitted successfully and is being processed',
        requestId: requestId
      }

    } catch (error) {
      logError('Vote request submission failed', error, undefined, {
        operationType: 'vote_submission_error',
        errorType: error instanceof Error ? error.constructor.name : 'unknown'
      })

      return {
        success: false,
        message: 'Failed to submit vote request. Please try again.',
        requestId: requestId,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * Perform basic validation before adding vote to stream
   * This reduces invalid votes in the stream and provides immediate feedback
   */
  private static async performBasicValidation(request: VoteSubmissionRequest): Promise<{
    isValid: boolean
    error?: string
  }> {
    try {
      // Validate input format
      if (!request.voterId || !request.tournamentId || 
          !request.firstSubmissionId || !request.secondSubmissionId || 
          !request.winnerSubmissionId) {
        return {
          isValid: false,
          error: 'All fields are required: voterId, tournamentId, firstSubmissionId, secondSubmissionId, winnerSubmissionId'
        }
      }

      // Validate winner is one of the contestants
      if (request.winnerSubmissionId !== request.firstSubmissionId && 
          request.winnerSubmissionId !== request.secondSubmissionId) {
        return {
          isValid: false,
          error: 'Winner must be one of the two contestants in the matchup'
        }
      }

      // Validate contestants are different
      if (request.firstSubmissionId === request.secondSubmissionId) {
        return {
          isValid: false,
          error: 'First and second submissions cannot be the same'
        }
      }

      // Check if tournament exists and is active
      const tournament = await DatabaseOperations.findTournamentById(request.tournamentId)
      if (!tournament) {
        return {
          isValid: false,
          error: 'Tournament not found'
        }
      }

      if (tournament.status !== TOURNAMENT_STATUS.ACTIVE) {
        return {
          isValid: false,
          error: 'Tournament is not currently active for voting'
        }
      }

      // Check if user already voted on this matchup (quick check to prevent obvious duplicates)
      const alreadyVoted = await DatabaseOperations.hasUserVotedOnMatchup(
        request.voterId,
        request.firstSubmissionId,
        request.secondSubmissionId
      )

      if (alreadyVoted) {
        return {
          isValid: false,
          error: 'You have already voted on this matchup'
        }
      }

      // Verify submissions exist and are active (basic check)
      const [firstSubmission, secondSubmission] = await Promise.all([
        DatabaseOperations.findSubmissionByIdWithUser(request.firstSubmissionId),
        DatabaseOperations.findSubmissionByIdWithUser(request.secondSubmissionId)
      ])

      if (!firstSubmission || !secondSubmission) {
        return {
          isValid: false,
          error: 'One or both contestants not found'
        }
      }

      // Check if submissions belong to the tournament
      if (firstSubmission.tournamentId !== request.tournamentId || 
          secondSubmission.tournamentId !== request.tournamentId) {
        return {
          isValid: false,
          error: 'Contestants must be from the specified tournament'
        }
      }

      // Check if submissions are active
      if ((firstSubmission as any).status !== SUBMISSION_STATUS.ACTIVE || 
          (secondSubmission as any).status !== SUBMISSION_STATUS.ACTIVE) {
        return {
          isValid: false,
          error: 'One or both contestants are no longer active in the tournament'
        }
      }

      // Check if user is trying to vote on their own submission
      if (firstSubmission.userId === request.voterId || secondSubmission.userId === request.voterId) {
        return {
          isValid: false,
          error: 'You cannot vote on matchups involving your own submission'
        }
      }

      // Verify contestants are in the same bracket
      if ((firstSubmission as any).currentBracket !== (secondSubmission as any).currentBracket) {
        return {
          isValid: false,
          error: 'Contestants must be in the same bracket'
        }
      }

      return { isValid: true }

    } catch (error) {
      logError('Vote validation failed', error, undefined, {
        operationType: 'vote_validation_error',
        errorType: error instanceof Error ? error.constructor.name : 'unknown'
      })
      return {
        isValid: false,
        error: 'Validation failed due to system error. Please try again.'
      }
    }
  }

  /**
   * Get queue status for monitoring
   */
  static async getQueueStatus(): Promise<{
    streamLength: number
    pendingMessages: number
    consumerGroups: number
  }> {
    try {
      const votingStream = redisManager.getVotingStream()
      const streamInfo = await votingStream.getStreamInfo()
      
      return {
        streamLength: streamInfo.length || 0,
        pendingMessages: streamInfo['pending-messages'] || 0,
        consumerGroups: streamInfo.groups || 0
      }
    } catch (error) {
      logError('Queue status check failed', error, undefined, {
        operationType: 'queue_status_error',
        errorType: error instanceof Error ? error.constructor.name : 'unknown'
      })
      return {
        streamLength: 0,
        pendingMessages: 0,
        consumerGroups: 0
      }
    }
  }
}
