import { Request, Response } from 'express'
import { hashPassword, comparePassword, generateToken } from '../utils/auth'
import { sendSuccess, sendError } from '../utils/responseWrapper'
import { DatabaseOperations } from '../database/DatabaseOperations'
import { HttpStatusCode } from '../enums/ErrorEnums'
import { logError, logInfo, logSecurityError } from '../utils/errorLogger'

const signup = async (req: Request, res: Response): Promise<void> => {
  const { email, password, username } = req.body
  try {
    // Input validation is now handled by middleware
    // Check if email already exists
    const existingEmail = await DatabaseOperations.findUserByEmail(email)
    if (existingEmail) {
      logSecurityError('Signup attempt with existing email',
        new Error('Email already exists'), req, { attemptType: 'signup_duplicate_email' })
      sendError(res, 'Email already exists', HttpStatusCode.BAD_REQUEST)
      return
    }

    // Check if username already exists
    const existingUsername = await DatabaseOperations.findUserByUsername(username)
    if (existingUsername) {
      logSecurityError('Signup attempt with existing username',
        new Error('Username already exists'), req, { attemptType: 'signup_duplicate_username' })
      sendError(res, 'Username already exists', HttpStatusCode.BAD_REQUEST)
      return
    }

    const passwordHash = await hashPassword(password)
    const user = await DatabaseOperations.createUser({
      email, passwordHash, username
    })
    logInfo('User created successfully', `User created: ${user.id}`, req, { userId: user.id })
    const token = generateToken(user.id)
    sendSuccess(
      res,
      {
        token,
        user: {
          id: user.id,
          email: user.email,
          username: user.username
        }
      },
      'User registered successfully',
      HttpStatusCode.CREATED
    )
  } catch (err) {
    logError('Signup failed', err, req, { attemptType: 'signup_system_error' })
    sendError(res, 'Signup failed', HttpStatusCode.INTERNAL_SERVER_ERROR)
  }
}

const login = async (req: Request, res: Response): Promise<void> => {
  const { email, password } = req.body
  try {
    // Input validation is now handled by middleware
    const user = await DatabaseOperations.findUserByEmail(email)
    if (!user) {
      logSecurityError('Login attempt with non-existent email',
        new Error('User not found'), req, { attemptType: 'login_invalid_email' })
      // Use generic message to prevent user enumeration
      sendError(res, 'Invalid credentials', HttpStatusCode.UNAUTHORIZED)
      return
    }

    const match = await comparePassword(password, user.passwordHash)
    if (!match) {
      logSecurityError('Login attempt with invalid password',
        new Error('Invalid credentials'), req, { attemptType: 'login_invalid_password' })
      sendError(res, 'Invalid credentials', HttpStatusCode.UNAUTHORIZED)
      return
    }

    const token = generateToken(user.id)
    sendSuccess(
      res,
      {
        token,
        user: {
          id: user.id,
          email: user.email,
          username: user.username,
          avatarUrl: user.avatarUrl,
          coins: user.coins,
          leagueLevel: user.leagueLevel,
          createdAt: user.createdAt
        }
      },
      'Login successful'
    )
    logInfo('User logged in successfully', `User logged in: ${user.id}`, req, { userId: user.id })
  } catch (err) {
    logError('Login failed', err, req, { attemptType: 'login_system_error' })
    sendError(res, 'Login failed', HttpStatusCode.INTERNAL_SERVER_ERROR)
  }
}

const getCurrentUser = async (req: Request, res: Response): Promise<void> => {
  const userId = (req as any).userId

  try {
    const user = await DatabaseOperations.findUserByIdWithSelect(userId, {
      id: true,
      email: true,
      username: true,
      avatarUrl: true,
      coins: true,
      leagueLevel: true,
      createdAt: true
    })

    if (!user) {
      logError('Current user not found', new Error('User not found'), req, { userId })
      sendError(res, 'User not found', HttpStatusCode.NOT_FOUND)
      return
    }
    logInfo('Current user fetched successfully', `Current user fetched: ${user.id}`, req, { userId: user.id })
    sendSuccess(res, { user }, 'Current user fetched successfully')
  } catch (err) {
    logError('Error fetching current user', err, req, { userId })
    sendError(res, 'Failed to fetch current user', HttpStatusCode.INTERNAL_SERVER_ERROR)
  }
}

const checkUsernameAvailability = async (req: Request, res: Response): Promise<void> => {
  const { username } = req.params

  try {

    const existingUser = await DatabaseOperations.findUserByUsername(username)
    const isAvailable = !existingUser

    sendSuccess(res, { available: isAvailable }, isAvailable ? 'Username is available' : 'Username is taken')
  } catch (err) {
    logError('Username availability check failed', err, req)
    sendError(res, 'Failed to check username availability', HttpStatusCode.INTERNAL_SERVER_ERROR)
  }
}

const getUserProfile = async (req: Request, res: Response): Promise<void> => {
  const { userId } = req.body

  try {
    // Validate that user ID is provided
    if (!userId) {
      sendError(res, 'User ID is required', HttpStatusCode.BAD_REQUEST)
      return
    }

    // Validate that userId is a valid UUID format
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i
    if (typeof userId !== 'string' || !uuidRegex.test(userId)) {
      sendError(res, 'Invalid user ID format', HttpStatusCode.BAD_REQUEST)
      return
    }

    const foundUser = await DatabaseOperations.findUserByIdWithSelect(userId, {
      id: true,
      username: true,
      avatarUrl: true,
      leagueLevel: true,
      createdAt: true
    })

    if (!foundUser) {
      logError('User profile not found', new Error('User not found'), req, { requestedUserId: userId })
      sendError(res, 'User not found', HttpStatusCode.NOT_FOUND)
      return
    }

    logInfo('Public user profile fetched successfully', `Public profile fetched: ${foundUser.id}`, req, {
      userId: foundUser.id
    })
    sendSuccess(res, { user: foundUser }, 'User profile fetched successfully')
  } catch (err) {
    logError('Error fetching user profile', err, req, { requestedUserId: userId })
    sendError(res, 'Failed to fetch user profile', HttpStatusCode.INTERNAL_SERVER_ERROR)
  }
}

export { signup, login, getCurrentUser, checkUsernameAvailability, getUserProfile }
