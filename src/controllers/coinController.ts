import { Request, Response } from 'express'
import { sendSuccess, sendError } from '../utils/responseWrapper'
import { DatabaseOperations } from '../database/DatabaseOperations'
import { HttpStatusCode } from '../enums/ErrorEnums'
import { logError } from '../utils/errorLogger'

const purchaseCoins = async (req: Request, res: Response): Promise<void> => {
  const { amount } = req.body
  const userId = (req as any).userId

  try {
    // Validate required fields
    if (!amount || typeof amount !== 'number' || amount <= 0) {
      sendError(res, 'Amount must be a positive number', 400)
      return
    }

    // Check if user exists
    const user = await DatabaseOperations.findUserById(userId)

    if (!user) {
      sendError(res, 'User not found', 404)
      return
    }

    // Use a transaction to ensure data consistency
    const result = await DatabaseOperations.createCoinPurchaseTransaction(
      userId,
      amount
    )

    sendSuccess(
      res,
      {
        transaction: result.coinTransaction,
        user: result.updatedUser
      },
      'Coins purchased successfully',
      201
    )
  } catch (err) {
    logError('Coin purchase failed', err, req, {
      userId,
      amount: req.body?.amount
    })
    sendError(res, 'Failed to purchase coins', 500)
  }
}

const getCoinTransactionHistory = async (req: Request, res: Response): Promise<void> => {
  const userId = (req as any).userId

  try {
    // Check if user exists
    const user = await DatabaseOperations.findUserByIdWithSelect(userId, {
      id: true, username: true, coins: true
    })

    if (!user) {
      sendError(res, 'User not found', 404)
      return
    }

    // Get user's coin transaction history
    const transactions = await DatabaseOperations.getCoinTransactionHistory(userId)

    sendSuccess(
      res,
      {
        user,
        transactions,
        totalTransactions: transactions.length
      },
      'Coin transaction history retrieved successfully'
    )
  } catch (err) {
    logError('Get coin transaction history failed', err, req)
    sendError(res, 'Failed to retrieve coin transaction history', HttpStatusCode.INTERNAL_SERVER_ERROR)
  }
}

export { purchaseCoins, getCoinTransactionHistory }
