import { Request, Response } from 'express'
import { sendSuccess, sendError } from '../utils/responseWrapper'
import { HttpStatusCode } from '../enums/ErrorEnums'
import { logError } from '../utils/errorLogger'
import {
  canAcceptNewContestant,
  startTournamentIfReady,
  getTournamentBracketInfo
} from '../services/singleEliminationService'
import {
  submitVoteAsync,
  getRandomMatchup,
  getTournamentRandomMatchup,
  getTournamentMatchups as getMatchupsForTournament
} from '../services/votingService'
import {
  MAX_CONTESTANTS,
  TOURNAMENT_STATUS,
  SUPPORTED_TOURNAMENT_SIZES,
  DEFAULT_TOURNAMENT_SIZE,
  isValidBracketSize,
  getDynamicTournamentConfig,
  getTournamentProgress
} from '../constants/tournamentConstants'
import { DatabaseOperations } from '../database/DatabaseOperations'

const createTournament = async (req: Request, res: Response): Promise<void> => {
  const { title, category, entryFee, maxContestants } = req.body
  const userId = (req as any).userId

  try {
    // Validate required fields
    if (!title || !category || entryFee === undefined || !maxContestants) {
      sendError(res, 'Title, category, entryFee, and maxContestants are required', 400)
      return
    }

    // Validate maxContestants is required and valid
    if (typeof maxContestants !== 'number' || maxContestants <= 0) {
      sendError(res, 'maxContestants must be a positive number', 400)
      return
    }

    if (!isValidBracketSize(maxContestants)) {
      sendError(res, `Invalid contestant count: ${maxContestants}. Check supported sizes.`, 400)
      return
    }

    // Validate entryFee
    if (typeof entryFee !== 'number' || entryFee < 0) {
      sendError(res, 'entryFee must be a non-negative number', 400)
      return
    }

    const tournament = await DatabaseOperations.createTournament({
      title,
      category,
      entryFee,
      createdById: userId,
      maxContestants,
    } as any)

    // Get dynamic tournament configuration for response
    const tournamentConfig = getDynamicTournamentConfig(maxContestants)

    sendSuccess(res, {
      tournament: {
        ...tournament,
        bracketInfo: {
          totalRounds: tournamentConfig.TOTAL_ROUNDS,
          bracketNames: tournamentConfig.BRACKET_NAMES,
          maxContestants
        }
      }
    }, 'Tournament created successfully', HttpStatusCode.CREATED)
  } catch (err) {
    logError('Tournament creation failed', err, req, {
      userId,
      title: req.body?.title
    })
    sendError(res, 'Failed to create tournament', 500)
  }
}

const listTournaments = async (_req: Request, res: Response): Promise<void> => {
  try {
    const tournaments = await DatabaseOperations.findTournamentsByStatusWithDetails([
      TOURNAMENT_STATUS.OPEN,
      TOURNAMENT_STATUS.ACTIVE,
      TOURNAMENT_STATUS.COMPLETED
    ])

    // Add contestant count and availability info to each tournament
    const tournamentsWithInfo = tournaments.map((tournament: any) => {
      const maxContestants = (tournament as any).maxContestants || MAX_CONTESTANTS
      const contestantCount = tournament._count.submissions
      const canAcceptContestants = tournament.status === TOURNAMENT_STATUS.OPEN &&
                                  contestantCount < maxContestants
      const spotsRemaining = maxContestants - contestantCount

      // Get tournament progress information
      const currentBracket = (tournament as any).currentBracket || 1
      const progressInfo = tournament.status === TOURNAMENT_STATUS.ACTIVE
        ? getTournamentProgress(currentBracket, maxContestants)
        : null

      return {
        ...tournament,
        contestantCount,
        canAcceptContestants,
        spotsRemaining,
        maxContestants,
        progressInfo
      }
    })

    sendSuccess(res, { tournaments: tournamentsWithInfo }, 'Tournaments fetched successfully')
  } catch (err) {
    logError('Tournament list fetch failed', err, _req)
    sendError(res, 'Failed to fetch tournaments', HttpStatusCode.INTERNAL_SERVER_ERROR)
  }
}

const submitToTournament = async (req: Request, res: Response): Promise<void> => {
  const { id: tournamentId } = req.params
  const { videoUrl } = req.body
  const userId = (req as any).userId

  try {
    // Check if user already has a submission for this tournament
    const existingSubmission = await DatabaseOperations.findSubmissionByUserAndTournament(userId, tournamentId)

    if (existingSubmission) {
      sendError(res, 'You already have a submission for this tournament', 400)
      return
    }

    // Get tournament and user information
    const [tournament, user] = await Promise.all([
      DatabaseOperations.findTournamentById(tournamentId),
      DatabaseOperations.findUserByIdWithSelect(userId, {
        id: true, coins: true, username: true
      })
    ])

    if (!tournament) {
      sendError(res, 'Tournament not found', 404)
      return
    }

    if (!user) {
      sendError(res, 'User not found', 404)
      return
    }

    if (tournament.status !== TOURNAMENT_STATUS.OPEN) {
      sendError(res, 'Tournament is not open for submissions', 400)
      return
    }

    // Check if tournament can accept new contestants
    const canAccept = await canAcceptNewContestant(tournamentId)
    if (!canAccept) {
      sendError(res, 'Tournament is full and cannot accept more contestants', 400)
      return
    }

    // Check if user has enough coins to pay the entry fee
    if (user.coins < tournament.entryFee) {
      sendError(res, `Insufficient coins. You need ${tournament.entryFee} coins but only have ${user.coins}`, 400)
      return
    }

    // Use a transaction to ensure data consistency
    const result = await DatabaseOperations.createSubmissionWithCoinTransaction(
      userId,
      tournamentId,
      videoUrl,
      tournament.entryFee
    )

    // Check if tournament should start (when max contestants reached)
    const tournamentStarted = await startTournamentIfReady(tournamentId)

    sendSuccess(
      res,
      {
        submission: result.submission,
        transaction: result.coinTransaction,
        user: result.updatedUser,
        tournamentStarted
      },
      tournamentStarted ?
        'Submission created successfully. Tournament has started!' :
        'Submission created successfully',
      201
    )
  } catch (err) {
    logError('Tournament submission failed', err, req, {
      tournamentId: req.params?.id,
      userId
    })
    sendError(res, 'Submission failed', HttpStatusCode.INTERNAL_SERVER_ERROR)
  }
}

const getSubmissionsForTournament = async (req: Request, res: Response): Promise<void> => {
  const { id: tournamentId } = req.params

  try {
    const submissions = await DatabaseOperations.findSubmissionsWithUserData(
      { tournamentId },
      [{ votes: 'desc' }]
    )

    sendSuccess(res, { submissions }, 'Submissions fetched successfully')
  } catch (err) {
    logError('Submissions fetch failed', err, req, {
      tournamentId: req.params?.id
    })
    sendError(res, 'Failed to fetch submissions', 500)
  }
}


const getTournament = async (req: Request, res: Response): Promise<void> => {
  const { id } = req.params

  try {
    const tournament = await DatabaseOperations.findTournamentById(id, {
      submissions: {
        include: {
          user: {
            select: { username: true, avatarUrl: true }
          }
        }
      },
      createdBy: {
        select: { username: true, avatarUrl: true }
      }
    })

    if (!tournament) {
      console.log('Tournament not found')
      sendError(res, 'Tournament not found', 404)
      return
    }

    sendSuccess(res, { tournament }, 'Tournament fetched successfully')
  } catch (err) {
    logError('Tournament fetch failed', err, req, {
      tournamentId: req.params?.id
    })
    sendError(res, 'Failed to fetch tournament', 500)
  }
}

const updateTournament = async (req: Request, res: Response): Promise<void> => {
  const { id } = req.params
  const { title } = req.body
  const tournament = (req as any).tournament

  try {
    const updatedTournament = await DatabaseOperations.updateTournament(id, {
      title
    })

    sendSuccess(res, { tournament: updatedTournament }, 'Tournament updated successfully')
  } catch (err) {
    logError('Tournament update failed', err, req, {
      tournamentId: req.params?.id
    })
    sendError(res, 'Failed to update tournament', HttpStatusCode.INTERNAL_SERVER_ERROR)
  }
}

const removeTournament = async (req: Request, res: Response): Promise<void> => {
  const { id } = req.params
  try {
    // Use a transaction to ensure data consistency
    await DatabaseOperations.executeTransaction(async (tx) => {
      // Delete all submissions related to this tournament
      await tx.submission.deleteMany({
        where: { tournamentId: id }
      })

      // Delete the tournament (coin transactions will be set to null automatically)
      await tx.tournament.delete({
        where: { id }
      })
    })

    sendSuccess(res, {}, 'Tournament deleted successfully')
  } catch (err) {
    logError('Tournament deletion failed', err, req, {
      tournamentId: req.params?.id
    })
    sendError(res, 'Failed to delete tournament', HttpStatusCode.INTERNAL_SERVER_ERROR)
  }
}

// Single-elimination tournament voting endpoints

const getTournamentMatchups = async (req: Request, res: Response): Promise<void> => {
  const { id: tournamentId } = req.params
  const userId = (req as any).userId

  try {
    const matchups = await getMatchupsForTournament(tournamentId, userId)

    if (matchups.length === 0) {
      sendError(res, 'No available matchups found for this tournament', 404)
      return
    }

    sendSuccess(res, { matchups }, 'Tournament matchups retrieved successfully')
  } catch (err) {
    logError('Tournament matchups fetch failed', err, req, {
      tournamentId: req.params?.id
    })
    sendError(res, 'Failed to get tournament matchups', 500)
  }
}

const voteInTournament = async (req: Request, res: Response): Promise<void> => {
  const { id: tournamentId } = req.params
  const { firstSubmissionId, secondSubmissionId, winnerSubmissionId } = req.body
  const voterId = (req as any).userId

  try {
    // Validate input
    if (!firstSubmissionId || !secondSubmissionId || !winnerSubmissionId) {
      sendError(res, 'firstSubmissionId, secondSubmissionId, and winnerSubmissionId are required', 400)
      return
    }

    if (firstSubmissionId === secondSubmissionId) {
      sendError(res, 'First and second submissions cannot be the same', 400)
      return
    }

    if (winnerSubmissionId !== firstSubmissionId && winnerSubmissionId !== secondSubmissionId) {
      sendError(res, 'Winner must be one of the two submissions in the matchup', 400)
      return
    }

    // Process the vote using Redis streams for better concurrency handling
    const result = await submitVoteAsync(
      voterId,
      tournamentId,
      firstSubmissionId,
      secondSubmissionId,
      winnerSubmissionId
    )

    if (result.success) {
      sendSuccess(res, {
        result,
        message: result.message,
        requestId: result.requestId
      }, 'Vote submitted successfully', HttpStatusCode.ACCEPTED) // 202 Accepted since it's async processing
    } else {
      sendError(res, result.message, 400)
    }
  } catch (err) {
    logError('Tournament voting failed', err, req, {
      tournamentId: req.params?.id
    })
    sendError(res, 'Voting failed', HttpStatusCode.INTERNAL_SERVER_ERROR)
  }
}

const getRandomMatchupController = async (req: Request, res: Response): Promise<void> => {
  const userId = (req as any).userId

  try {
    const matchup = await getRandomMatchup(userId)

    if (!matchup) {
      sendError(res, 'No available random matchups found', 404)
      return
    }

    sendSuccess(res, { matchup }, 'Random matchup retrieved successfully')
  } catch (err) {
    logError('Random matchup fetch failed', err, req)
    sendError(res, 'Failed to get random matchup', HttpStatusCode.INTERNAL_SERVER_ERROR)
  }
}

const getTournamentRandomMatchupController = async (req: Request, res: Response): Promise<void> => {
  const { id: tournamentId } = req.params
  const userId = (req as any).userId

  console.log('getTournamentRandomMatchupController', tournamentId, userId)
  try {
    const matchup = await getTournamentRandomMatchup(tournamentId, userId)

    if (!matchup) {
      sendError(res, 'No available matchups found for this tournament', 404)
      return
    }

    sendSuccess(res, { matchup }, 'Tournament random matchup retrieved successfully')
  } catch (err) {
    logError('Tournament random matchup fetch failed', err, req, {
      tournamentId: req.params?.id
    })
    sendError(res, 'Failed to get tournament random matchup', HttpStatusCode.INTERNAL_SERVER_ERROR)
  }
}

const getSupportedTournamentSizes = async (_req: Request, res: Response): Promise<void> => {
  try {
    const sizes = SUPPORTED_TOURNAMENT_SIZES.map(size => ({
      contestants: size,
      totalRounds: Math.log2(size),
      description: `${size} contestants tournament (${Math.log2(size)} rounds)`
    }))

    sendSuccess(res, {
      supportedSizes: sizes,
      defaultSize: DEFAULT_TOURNAMENT_SIZE,
    }, 'Supported tournament sizes retrieved successfully')
  } catch (err) {
    logError('Tournament sizes fetch failed', err, _req)
    sendError(res, 'Failed to get supported tournament sizes', HttpStatusCode.INTERNAL_SERVER_ERROR)
  }
}

const getTournamentBracket = async (req: Request, res: Response): Promise<void> => {
  const { id: tournamentId } = req.params

  try {
    const bracketInfo = await getTournamentBracketInfo(tournamentId)

    sendSuccess(res, {
      tournament: bracketInfo.tournament,
      currentBracketMatchups: bracketInfo.currentBracketMatchups,
      allBracketMatchups: bracketInfo.allBracketMatchups,
      bracketConfiguration: bracketInfo.bracketConfiguration,
      contestants: bracketInfo.contestants,
      winner: bracketInfo.winner,
      summary: {
        totalContestants: bracketInfo.contestants.length,
        activeContestants: bracketInfo.contestants.filter(c => c.status === 'active').length,
        eliminatedContestants: bracketInfo.contestants.filter(c => c.status === 'eliminated').length,
        currentBracketMatchupsCount: bracketInfo.currentBracketMatchups.length,
        totalMatchupsCount: Object.values(bracketInfo.allBracketMatchups).reduce((sum, matchups) => sum + matchups.length, 0)
      }
    }, 'Tournament bracket information retrieved successfully')
  } catch (err) {
    logError('Tournament bracket fetch failed', err, req, {
      tournamentId: req.params?.id
    })
    sendError(res, 'Failed to get tournament bracket information', 500)
  }
}

export {
  createTournament,
  listTournaments,
  submitToTournament,
  getSubmissionsForTournament,
  getTournament,
  updateTournament,
  removeTournament,
  getTournamentMatchups,
  voteInTournament,
  getRandomMatchupController,
  getTournamentRandomMatchupController,
  getSupportedTournamentSizes,
  getTournamentBracket
}
