import { Request, Response, Express } from 'express'
import { voteConsumer } from '../redis/VoteConsumer'
import { logError } from '../utils/errorLogger'
import { HttpStatusCode } from '../enums/ErrorEnums'

/**
 * HealthCheckService - System health monitoring
 * 
 * Provides health check endpoints to monitor the status of various
 * system components including Redis, database, and voting system.
 */
export class HealthCheckService {

  /**
   * Health check for Redis voting system
   * Provides detailed status information about the voting consumer and Redis connection
   * Note: In microservices architecture, the consumer runs in a separate container
   */
  static async checkVotingSystemHealth(req: Request, res: Response): Promise<void> {
    try {
      const status = await voteConsumer.getStatus()

      // In microservices architecture, check if Redis is healthy and consumer container exists
      // The consumer runs in a separate container, so we check Redis health instead
      const isVotingSystemHealthy = status.redisHealth.status === 'healthy'

      const healthResponse = {
        status: isVotingSystemHealthy ? 'healthy' : 'unhealthy',
        architecture: 'microservices',
        consumer: {
          note: 'Consumer runs in separate container/process',
          localInstance: status.isRunning,
          redisConnected: status.redisHealth.status === 'healthy'
        },
        redis: status.redisHealth,
        queue: status.queueMetrics,
        timestamp: new Date().toISOString()
      }

      res.json(healthResponse)
    } catch (error) {
      // Log error details for debugging
      logError('Health check failed', error, req)

      // Return sanitized error response
      const errorResponse = {
        status: 'unhealthy',
        error: process.env.NODE_ENV === 'production' ?
          'Service unavailable' :
          (error instanceof Error ? error.message : 'Unknown error'),
        timestamp: new Date().toISOString()
      }

      res.status(HttpStatusCode.INTERNAL_SERVER_ERROR).json(errorResponse)
    }
  }

  /**
   * Basic application health check
   * Simple endpoint to verify the application is running
   */
  static async checkApplicationHealth(_req: Request, res: Response): Promise<void> {
    const healthResponse = {
      status: 'healthy',
      application: 'GetRankt API',
      version: process.env.npm_package_version || '1.0.0',
      environment: process.env.NODE_ENV || 'development',
      uptime: process.uptime(),
      timestamp: new Date().toISOString()
    }

    res.json(healthResponse)
  }

  /**
   * Comprehensive system health check
   * Checks multiple system components
   */
  static async checkSystemHealth(req: Request, res: Response): Promise<void> {
    try {
      const checks = await Promise.allSettled([
        // Check voting system
        voteConsumer.getStatus(),
        // Add other health checks here as needed
        // e.g., database connectivity, external services
      ])

      const votingSystemCheck = checks[0]
      // In microservices architecture, check Redis health instead of local consumer
      const isVotingHealthy = votingSystemCheck.status === 'fulfilled' &&
        (votingSystemCheck.value as any).redisHealth?.status === 'healthy'

      const overallStatus = isVotingHealthy ? 'healthy' : 'degraded'

      const healthResponse = {
        status: overallStatus,
        checks: {
          votingSystem: {
            status: isVotingHealthy ? 'healthy' : 'unhealthy',
            details: votingSystemCheck.status === 'fulfilled' ? 
              votingSystemCheck.value : 
              { error: 'Check failed' }
          }
        },
        timestamp: new Date().toISOString()
      }

      const statusCode = overallStatus === 'healthy' ? HttpStatusCode.OK : HttpStatusCode.SERVICE_UNAVAILABLE
      res.status(statusCode).json(healthResponse)
    } catch (error) {
      logError('System health check failed', error, req)

      res.status(HttpStatusCode.SERVICE_UNAVAILABLE).json({
        status: 'unhealthy',
        error: process.env.NODE_ENV === 'production' ? 
          'System check failed' : 
          (error instanceof Error ? error.message : 'Unknown error'),
        timestamp: new Date().toISOString()
      })
    }
  }

  /**
   * Register all health check endpoints
   */
  static registerEndpoints(app: Express): void {
    // Docker health check endpoint (simple path for container health checks)
    app.get('/health', this.checkApplicationHealth)

    // Voting system specific health check
    app.get('/api/health/voting', this.checkVotingSystemHealth)

    // Basic application health check
    app.get('/api/health', this.checkApplicationHealth)

    // Comprehensive system health check
    app.get('/api/health/system', this.checkSystemHealth)
  }
}
