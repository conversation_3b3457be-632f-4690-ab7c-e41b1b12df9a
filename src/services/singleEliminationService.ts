import {
  VOTES_TO_ADVANCE,
  TOURNAMENT_STATUS,
  SUBMISSION_STATUS,
  getNextBracketRound,
  getContestantsToAdvanceFromBracket,
  getFirstRound,
  isFinalRound,
  getDynamicTournamentConfig,
  getTournamentProgress
} from '../constants/tournamentConstants'
import { DatabaseOperations } from '../database/DatabaseOperations'

interface TournamentContestant {
  id: string
  userId: string
  videoUrl: string
  votes: number
  currentBracket: number
  status: string
  bracketPosition: number | null
  user: {
    username: string
    avatarUrl: string | null
  }
}

interface BracketMatchup {
  id: string
  firstSubmissionId: string
  secondSubmissionId: string
  bracketRound: number
  isActive: boolean
  firstSubmission: TournamentContestant
  secondSubmission: TournamentContestant
  promotedUserId: string | null // User ID of the contestant currently winning (more votes)
}

interface TournamentInfo {
  id: string
  title: string
  category: string
  status: string
  currentBracket: number
  maxContestants: number
  contestantCount: number
  canAcceptContestants: boolean
}

/**
 * Check if tournament can accept new contestants
 */
export const canAcceptNewContestant = async (tournamentId: string): Promise<boolean> => {
  const tournament = await fetchTournamentWithCount(tournamentId)
  return canTournamentAcceptContestants(tournament)
}

/**
 * Fetch tournament with submission count from database
 */
const fetchTournamentWithCount = async (tournamentId: string) => {
  const tournament = await DatabaseOperations.findTournamentWithCount(tournamentId)

  if (!tournament) {
    throw new Error('Tournament not found')
  }

  return tournament
}

/**
 * Check if tournament can accept new contestants
 */
const canTournamentAcceptContestants = (tournament: any): boolean => {
  return tournament.status === TOURNAMENT_STATUS.OPEN &&
         tournament._count.submissions < tournament.maxContestants
}

/**
 * Get tournament information with contestant count
 */
export const getTournamentInfo = async (tournamentId: string): Promise<TournamentInfo> => {
  const tournament = await fetchTournamentWithCount(tournamentId)

  return {
    id: tournament.id,
    title: tournament.title,
    category: tournament.category,
    status: tournament.status,
    currentBracket: tournament.currentBracket,
    maxContestants: tournament.maxContestants,
    contestantCount: tournament._count.submissions,
    canAcceptContestants: canTournamentAcceptContestants(tournament)
  }
}

/**
 * Check if tournament is ready to start
 */
const isTournamentReadyToStart = (tournament: any): boolean => {
  return tournament.status === TOURNAMENT_STATUS.OPEN &&
         tournament._count.submissions === tournament.maxContestants
}

/**
 * Initialize tournament bracket
 */
const initializeTournamentBracket = async (tournamentId: string, firstRound: number, tx: any): Promise<void> => {
  await DatabaseOperations.initializeTournamentBracket(tournamentId, firstRound, tx)
  await generateBracketMatchups(tournamentId, firstRound, tx)
}

/**
 * Start tournament when max contestants reached
 */
export const startTournamentIfReady = async (tournamentId: string): Promise<boolean> => {
  const tournament = await fetchTournamentWithCount(tournamentId)

  if (!isTournamentReadyToStart(tournament)) {
    return false
  }

  const firstRound = getFirstRound()

  await DatabaseOperations.executeTransaction(async (tx: any) => {
    await initializeTournamentBracket(tournamentId, firstRound, tx)
  })

  return true
}

/**
 * Fetch active contestants for bracket
 */
const fetchActiveContestantsForBracket = async (
  tournamentId: string,
  bracketRound: number,
  tx?: any
) => {
  return await DatabaseOperations.findActiveContestantsForBracket(tournamentId, bracketRound, tx)
}

/**
 * Shuffle array using Fisher-Yates algorithm
 */
const shuffleArray = <T>(array: T[]): T[] => {
  const shuffled = [...array]
  for (let i = shuffled.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]]
  }
  return shuffled
}

/**
 * Assign bracket positions to contestants
 */
const assignBracketPositions = async (contestants: any[], tx?: any): Promise<void> => {
  await DatabaseOperations.assignBracketPositions(contestants, tx)
}

/**
 * Create matchups from paired contestants
 */
const createMatchupsFromContestants = async (
  contestants: any[],
  tournamentId: string,
  bracketRound: number,
  tx?: any
): Promise<void> => {
  await DatabaseOperations.createBracketMatchups(contestants, tournamentId, bracketRound, tx)
}

/**
 * Generate bracket matchups for a specific round
 */
const generateBracketMatchups = async (
  tournamentId: string,
  bracketRound: number,
  tx?: any
): Promise<void> => {
  const contestants = await fetchActiveContestantsForBracket(tournamentId, bracketRound, tx)
  const shuffledContestants = shuffleArray(contestants)

  await assignBracketPositions(shuffledContestants, tx)
  await createMatchupsFromContestants(shuffledContestants, tournamentId, bracketRound, tx)
}

/**
 * Fetch tournament by ID
 */
const fetchTournament = async (tournamentId: string) => {
  const tournament = await DatabaseOperations.findTournamentById(tournamentId)

  if (!tournament) {
    throw new Error('Tournament not found')
  }

  return tournament
}

/**
 * Map submission to tournament contestant
 */
const mapToTournamentContestant = (contestant: any): TournamentContestant => ({
  id: contestant.id,
  userId: contestant.userId,
  videoUrl: contestant.videoUrl,
  votes: contestant.votes,
  currentBracket: contestant.currentBracket,
  status: contestant.status,
  bracketPosition: contestant.bracketPosition,
  user: contestant.user
})

/**
 * Fetch contestants with user data
 */
const fetchContestantsWithUserData = async (
  tournamentId: string,
  targetBracket: number,
  status: string = SUBMISSION_STATUS.ACTIVE
) => {
  return await DatabaseOperations.findSubmissionsWithUserData(
    {
      tournamentId,
      currentBracket: targetBracket,
      status
    } as any,
    [
      { votes: 'desc' },
      { createdAt: 'asc' }
    ]
  )
}

/**
 * Get active contestants in current bracket
 */
export const getActiveContestants = async (
  tournamentId: string,
  bracketRound?: number
): Promise<TournamentContestant[]> => {
  const tournament = await fetchTournament(tournamentId)
  const targetBracket = bracketRound || tournament.currentBracket

  const contestants = await fetchContestantsWithUserData(tournamentId, targetBracket)
  return contestants.map(mapToTournamentContestant)
}

/**
 * Fetch matchups with submission data
 */
const fetchMatchupsWithSubmissionData = async (
  tournamentId: string,
  targetBracket: number,
  isActive: boolean = true
) => {
  return await DatabaseOperations.findMatchupsWithSubmissionData(
    {
      tournamentId,
      bracketRound: targetBracket,
      isActive
    }
  )
}

/**
 * Map matchup data to bracket matchup
 */
const mapToBracketMatchup = (matchup: any): BracketMatchup => {
  const firstSubmission = mapToTournamentContestant(matchup.firstSubmission)
  const secondSubmission = mapToTournamentContestant(matchup.secondSubmission)

  // Determine promoted user ID (contestant with more votes)
  let promotedUserId: string | null = null
  if (firstSubmission.votes > secondSubmission.votes) {
    promotedUserId = firstSubmission.userId
  } else if (secondSubmission.votes > firstSubmission.votes) {
    promotedUserId = secondSubmission.userId
  }
  // If votes are tied, promotedUserId remains null

  return {
    id: matchup.id,
    firstSubmissionId: matchup.firstSubmissionId,
    secondSubmissionId: matchup.secondSubmissionId,
    bracketRound: matchup.bracketRound,
    isActive: matchup.isActive,
    firstSubmission,
    secondSubmission,
    promotedUserId
  }
}

/**
 * Get active matchups for current bracket
 */
export const getActiveMatchups = async (
  tournamentId: string,
  bracketRound?: number
): Promise<BracketMatchup[]> => {
  const tournament = await fetchTournament(tournamentId)
  const targetBracket = bracketRound || tournament.currentBracket

  const matchups = await fetchMatchupsWithSubmissionData(tournamentId, targetBracket)
  return matchups.map(mapToBracketMatchup)
}

/**
 * Calculate votes required to advance from a specific bracket
 */
const getVotesToAdvanceFromBracket = (bracketRound: number): number => {
  return VOTES_TO_ADVANCE * bracketRound
}

/**
 * Fetch qualified contestants who reached vote threshold for their current bracket
 */
const fetchQualifiedContestants = async (
  tournamentId: string,
  currentBracket: number
) => {
  const requiredVotes = getVotesToAdvanceFromBracket(currentBracket)

  return await DatabaseOperations.findSubmissionsWithUserData(
    {
      tournamentId,
      currentBracket,
      status: SUBMISSION_STATUS.ACTIVE,
      votes: { gte: requiredVotes }
    },
    [
      { votes: 'desc' },
      { createdAt: 'asc' }
    ]
  )
}

/**
 * Deactivate matchups involving contestants who have reached promotion threshold
 */
export const deactivateMatchupsForQualifiedContestants = async (
  tournamentId: string,
  currentBracket: number,
  tx?: any
): Promise<number> => {
  // Get qualified contestants (those who reached vote threshold)
  const qualifiedContestants = await fetchQualifiedContestants(tournamentId, currentBracket)

  if (qualifiedContestants.length === 0) {
    return 0
  }

  const qualifiedIds = qualifiedContestants.map((c: any) => c.id)

  // Deactivate matchups where either contestant has reached the promotion threshold
  const result = await DatabaseOperations.deactivateMatchupsForQualifiedContestants(
    tournamentId,
    currentBracket,
    qualifiedIds,
    tx
  )

  return result.count
}

/**
 * Check if all contestants in a bracket have reached the vote threshold
 */
const isBracketComplete = async (
  tournamentId: string,
  currentBracket: number,
  maxContestants: number
): Promise<boolean> => {
  // Get the number of contestants that should advance from this bracket
  const targetAdvancementCount = getContestantsToAdvanceFromBracket(maxContestants, currentBracket)

  // Get qualified contestants (those who reached vote threshold)
  const qualifiedContestants = await fetchQualifiedContestants(tournamentId, currentBracket)

  // Bracket is complete if we have enough qualified contestants to fill the advancement slots
  return qualifiedContestants.length >= targetAdvancementCount
}

/**
 * Get contestants to advance and eliminate
 */
const getAdvancementGroups = async (
  tournamentId: string,
  currentBracket: number,
  maxContestants: number
) => {
  const qualifiedContestants = await fetchQualifiedContestants(tournamentId, currentBracket)
  const targetAdvancementCount = getContestantsToAdvanceFromBracket(maxContestants, currentBracket)
  const advancingContestants = qualifiedContestants.slice(0, targetAdvancementCount)

  if (advancingContestants.length === 0) {
    return { advancingContestants: [], eliminatingContestants: [] }
  }

  const allActiveContestants = await fetchContestantsWithUserData(
    tournamentId,
    currentBracket,
    SUBMISSION_STATUS.ACTIVE
  )

  const advancingIds = advancingContestants.map((c: any) => c.id)
  const eliminatingContestants = allActiveContestants.filter((c: any) => !advancingIds.includes(c.id))

  return { advancingContestants, eliminatingContestants }
}

/**
 * Advance contestants to next bracket
 */
const advanceContestantsToNextBracket = async (
  advancingContestants: any[],
  nextBracket: number,
  tournamentId: string,
  tx: any
): Promise<void> => {
  // Update contestants for next bracket (preserve vote counts) and set them to ACTIVE immediately
  await DatabaseOperations.advanceContestantsToNextBracket(advancingContestants, nextBracket, tx)

  // Update tournament to next bracket
  await DatabaseOperations.updateTournamentToNextBracket(tournamentId, nextBracket, tx)

  // Generate matchups for next bracket (now contestants are ACTIVE and in the correct bracket)
  await generateBracketMatchups(tournamentId, nextBracket, tx)
}

/**
 * Complete tournament with winner
 */
const completeTournamentWithWinner = async (
  advancingContestants: any[],
  currentBracket: number,
  maxContestants: number,
  tournamentId: string,
  tx: any
): Promise<boolean> => {
  if (advancingContestants.length === 1 && isFinalRound(currentBracket, maxContestants)) {
    await DatabaseOperations.completeTournamentWithWinner(tournamentId, advancingContestants[0].id, tx)
    return true
  }
  return false
}

/**
 * Eliminate contestants and deactivate matchups
 */
const eliminateContestantsAndDeactivateMatchups = async (
  eliminatingContestants: any[],
  tournamentId: string,
  currentBracket: number,
  tx: any
): Promise<void> => {
  // Eliminate non-advancing contestants
  await DatabaseOperations.eliminateContestants(eliminatingContestants, tx)

  // Deactivate current bracket matchups
  await DatabaseOperations.deactivateBracketMatchups(tournamentId, currentBracket, tx)
}

/**
 * Check and advance contestants who reached vote threshold
 */
export const checkAndAdvanceContestants = async (tournamentId: string): Promise<{
  advancedContestants: TournamentContestant[]
  eliminatedContestants: TournamentContestant[]
  nextBracketStarted: boolean
  tournamentCompleted: boolean
}> => {
  const tournament = await fetchTournament(tournamentId)
  const currentBracket = tournament.currentBracket
  const nextBracket = getNextBracketRound(currentBracket, tournament.maxContestants)

  // Check if the bracket is complete (enough contestants have reached vote threshold)
  const bracketComplete = await isBracketComplete(tournamentId, currentBracket, tournament.maxContestants)

  if (!bracketComplete) {
    // Bracket is not complete yet, don't advance anyone
    return {
      advancedContestants: [],
      eliminatedContestants: [],
      nextBracketStarted: false,
      tournamentCompleted: false
    }
  }

  const { advancingContestants, eliminatingContestants } = await getAdvancementGroups(
    tournamentId,
    currentBracket,
    tournament.maxContestants
  )

  if (advancingContestants.length === 0) {
    return {
      advancedContestants: [],
      eliminatedContestants: [],
      nextBracketStarted: false,
      tournamentCompleted: false
    }
  }

  let nextBracketStarted = false
  let tournamentCompleted = false

  await DatabaseOperations.executeTransaction(async (tx: any) => {
    if (nextBracket) {
      await advanceContestantsToNextBracket(advancingContestants, nextBracket, tournamentId, tx)
      nextBracketStarted = true
    } else {
      tournamentCompleted = await completeTournamentWithWinner(
        advancingContestants,
        currentBracket,
        tournament.maxContestants,
        tournamentId,
        tx
      )
    }

    await eliminateContestantsAndDeactivateMatchups(
      eliminatingContestants,
      tournamentId,
      currentBracket,
      tx
    )
  })

  return {
    advancedContestants: advancingContestants.map(mapToTournamentContestant),
    eliminatedContestants: eliminatingContestants.map(mapToTournamentContestant),
    nextBracketStarted,
    tournamentCompleted
  }
}

/**
 * Fetch all contestants for tournament
 */
const fetchAllTournamentContestants = async (tournamentId: string) => {
  return await DatabaseOperations.findSubmissionsWithUserData(
    { tournamentId },
    [
      { currentBracket: 'desc' },
      { votes: 'desc' },
      { createdAt: 'asc' }
    ]
  )
}

/**
 * Fetch all matchups for tournament
 */
const fetchAllTournamentMatchups = async (tournamentId: string) => {
  return await DatabaseOperations.findMatchupsWithSubmissionData(
    { tournamentId },
    [
      { bracketRound: 'asc' },
      { createdAt: 'asc' }
    ]
  )
}

/**
 * Group matchups by bracket round
 */
const groupMatchupsByBracket = (
  allMatchups: any[],
  currentBracket: number
): { allBracketMatchups: Record<number, BracketMatchup[]>, currentBracketMatchups: BracketMatchup[] } => {
  const allBracketMatchups: Record<number, BracketMatchup[]> = {}
  let currentBracketMatchups: BracketMatchup[] = []

  allMatchups.forEach((matchup: any) => {
    const bracketMatchup = mapToBracketMatchup(matchup)

    if (!allBracketMatchups[matchup.bracketRound]) {
      allBracketMatchups[matchup.bracketRound] = []
    }
    allBracketMatchups[matchup.bracketRound].push(bracketMatchup)

    if (matchup.bracketRound === currentBracket) {
      currentBracketMatchups.push(bracketMatchup)
    }
  })

  return { allBracketMatchups, currentBracketMatchups }
}

/**
 * Create bracket configuration object
 */
const createBracketConfiguration = (bracketConfig: any) => ({
  totalRounds: bracketConfig.TOTAL_ROUNDS,
  bracketNames: bracketConfig.BRACKET_NAMES,
  contestantsPerBracket: bracketConfig.CONTESTANTS_PER_BRACKET,
  contestantsToAdvancePerBracket: bracketConfig.CONTESTANTS_TO_ADVANCE_PER_BRACKET
})

/**
 * Get comprehensive bracket information for display purposes
 */
export const getTournamentBracketInfo = async (tournamentId: string): Promise<{
  tournament: {
    id: string
    title: string
    category: string
    status: string
    currentBracket: number
    maxContestants: number
    totalRounds: number
    progressInfo: any
  }
  currentBracketMatchups: BracketMatchup[]
  allBracketMatchups: Record<number, BracketMatchup[]>
  bracketConfiguration: any
  contestants: TournamentContestant[]
  winner: TournamentContestant | null
}> => {
  const tournament = await fetchTournamentWithCount(tournamentId)
  const maxContestants = (tournament as any).maxContestants || 64
  const currentBracket = tournament.currentBracket

  // Get dynamic tournament configuration
  const bracketConfig = getDynamicTournamentConfig(maxContestants)
  const progressInfo = getTournamentProgress(currentBracket, maxContestants)

  // Get all contestants and matchups
  const contestants = await fetchAllTournamentContestants(tournamentId)
  const allMatchups = await fetchAllTournamentMatchups(tournamentId)

  // Get tournament winner if exists
  const winner = await DatabaseOperations.findTournamentWinner(tournamentId)

  // Group matchups by bracket
  const { allBracketMatchups, currentBracketMatchups } = groupMatchupsByBracket(
    allMatchups,
    currentBracket
  )

  return {
    tournament: {
      id: tournament.id,
      title: tournament.title,
      category: tournament.category,
      status: tournament.status,
      currentBracket,
      maxContestants,
      totalRounds: bracketConfig.TOTAL_ROUNDS,
      progressInfo
    },
    currentBracketMatchups,
    allBracketMatchups,
    bracketConfiguration: createBracketConfiguration(bracketConfig),
    contestants: contestants.map(mapToTournamentContestant),
    winner: winner ? mapToTournamentContestant(winner) : null
  }
}
