import { Express } from 'express'
import express from 'express'
import helmet from 'helmet'
import { securityHeaders } from '../middlewares/securityMiddleware'
import { globalErrorHandler, notFoundHandler } from '../middlewares/errorMiddleware'

/**
 * MiddlewareConfig - Centralized middleware configuration
 * 
 * This class handles all middleware setup for the Express application,
 * including security, parsing, and error handling middleware.
 */
export class MiddlewareConfig {
  
  /**
   * Configure security middleware
   * Optimized for API-only backend with appropriate headers
   */
  static configureSecurity(app: Express): void {
    // Helmet security middleware
    app.use(helmet({
      // Disable browser-specific headers (not needed for API)
      contentSecurityPolicy: false,
      crossOriginEmbedderPolicy: false,
      crossOriginOpenerPolicy: false,
      crossOriginResourcePolicy: false,
      xssFilter: false,
      frameguard: false,
      referrerPolicy: false,

      // Enable API-relevant security features
      hsts: {
        maxAge: 31536000, // 1 year
        includeSubDomains: true,
        preload: true
      },
      noSniff: true, // Prevent MIME type sniffing
    }))

    // Custom security headers middleware
    app.use(securityHeaders)
  }

  /**
   * Configure request parsing middleware
   */
  static configureRequestParsing(app: Express): void {
    // JSON parsing with size limits
    app.use(express.json({ limit: '1mb' }))
    
    // URL-encoded parsing (if needed for form data)
    // app.use(express.urlencoded({ extended: true, limit: '1mb' }))
  }

  /**
   * Configure error handling middleware
   * IMPORTANT: Must be called after all routes are registered
   */
  static configureErrorHandling(app: Express): void {
    // Handle 404 errors for undefined routes
    app.use(notFoundHandler)
    
    // Handle all other errors with sanitization
    app.use(globalErrorHandler)
  }

  /**
   * Configure all middleware in the correct order
   */
  static configureAll(app: Express): void {
    this.configureSecurity(app)
    this.configureRequestParsing(app)
    // Note: Error handling middleware should be configured after routes
  }
}
