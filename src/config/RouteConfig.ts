import { Express } from 'express'

// Route handlers
import authRoutes from '../routes/authRoutes'
import tournamentRoutes from '../routes/tournamentRoutes'
import coinRoutes from '../routes/coinRoutes'

/**
 * RouteConfig - Centralized route configuration
 * 
 * This class handles the registration of all API routes,
 * providing a clean separation of concerns and easy route management.
 */
export class RouteConfig {

  /**
   * Register authentication routes
   */
  static registerAuthRoutes(app: Express): void {
    app.use('/api/auth', authRoutes)
  }

  /**
   * Register tournament routes
   */
  static registerTournamentRoutes(app: Express): void {
    app.use('/api/tournaments', tournamentRoutes)
  }

  /**
   * Register coin/payment routes
   */
  static registerCoinRoutes(app: Express): void {
    app.use('/api/coin', coinRoutes)
  }

  /**
   * Register all API routes
   */
  static registerAllRoutes(app: Express): void {
    this.registerAuthRoutes(app)
    this.registerTournamentRoutes(app)
    this.registerCoinRoutes(app)
  }
}
