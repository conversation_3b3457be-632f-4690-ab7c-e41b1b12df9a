import { redisManager } from '../redis/RedisConfig'

/**
 * ServerInitializer - Application initialization logic
 * 
 * Handles the initialization sequence for various system components
 * including Redis, database connections, and other services.
 */
export class ServerInitializer {

  /**
   * Initialize Redis voting system
   * Sets up Redis connection and voting stream for asynchronous vote processing
   */
  static async initializeVotingSystem(): Promise<void> {
    try {
      console.log('🔄 Initializing Redis voting system...')
      
      await redisManager.initializeVotingStream()
      
      console.log('✅ Redis voting system initialized successfully')
    } catch (error) {
      console.error('❌ Failed to initialize Redis voting system:', error)
      console.error('⚠️ Application will continue without Redis voting (falling back to synchronous voting)')
      
      // Don't throw error - allow application to continue without <PERSON>is
    }
  }

  /**
   * Initialize all system components in the correct order
   */
  static async initializeAll(): Promise<void> {
    
    try {
      // Initialize optional components
      await Promise.allSettled([
        this.initializeVotingSystem(),
      ])
    } catch (error) {
      console.error('💥 Critical system initialization failed:', error)
      throw error
    }
  }

  /**
   * Graceful shutdown handler
   * <PERSON><PERSON>ly closes connections and cleans up resources
   */
  static async gracefulShutdown(signal: string): Promise<void> {
    console.log(`📨 Received ${signal} signal, starting graceful shutdown...`)
    
    try {
      // Close Redis connections
      if (redisManager) {
        console.log('🔄 Closing Redis connections...')
        // Add Redis cleanup logic here
      }
      
      // Close database connections
      console.log('🔄 Closing database connections...')
      // Add database cleanup logic here
      
      console.log('✅ Graceful shutdown completed')
      process.exit(0)
    } catch (error) {
      console.error('❌ Error during graceful shutdown:', error)
      process.exit(1)
    }
  }

  /**
   * Setup process signal handlers for graceful shutdown
   */
  static setupGracefulShutdown(): void {
    // Handle different termination signals
    process.on('SIGTERM', () => this.gracefulShutdown('SIGTERM'))
    process.on('SIGINT', () => this.gracefulShutdown('SIGINT'))
    
    // Handle uncaught exceptions
    process.on('uncaughtException', (error) => {
      console.error('💥 Uncaught Exception:', error)
      this.gracefulShutdown('UNCAUGHT_EXCEPTION')
    })
    
    process.on('unhandledRejection', (reason, promise) => {
      console.error('💥 Unhandled Rejection at:', promise, 'reason:', reason)
      this.gracefulShutdown('UNHANDLED_REJECTION')
    })
  }
}
