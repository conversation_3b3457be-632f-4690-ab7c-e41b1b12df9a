/**
 * Tournament Configuration Constants
 * 
 * This file contains all configurable constants for the single-elimination tournament system.
 * Modify these values to adjust tournament behavior without changing core logic.
 */

// Tournament Structure Constants
export const MAX_CONTESTANTS = 64 // Default maximum number of contestants (can be overridden per tournament)
export const VOTES_TO_ADVANCE = 3 // Number of votes required to advance to next bracket

// Supported tournament sizes (all powers of 2)
export const SUPPORTED_TOURNAMENT_SIZES = [4, 16, 32, 64, 128] as const
export const DEFAULT_TOURNAMENT_SIZE = 64

// Tournament Status Constants
export const TOURNAMENT_STATUS = {
  OPEN: 'open',           // Accepting new contestants
  ACTIVE: 'active',       // Tournament is running with voting
  COMPLETED: 'completed', // Tournament has finished
  CANCELLED: 'cancelled'  // Tournament was cancelled
} as const

// Submission Status Constants
export const SUBMISSION_STATUS = {
  ACTIVE: 'active',         // Still competing in current bracket
  ELIMINATED: 'eliminated', // Eliminated from tournament
  ADVANCED: 'advanced',     // Advanced to next bracket
  WINNER: 'winner'          // Tournament winner
} as const

// Bracket Constants
export const BRACKET_ROUNDS = {
  ROUND_OF_64: 1,  // First round (64 → 32)
  ROUND_OF_32: 2,  // Second round (32 → 16)
  ROUND_OF_16: 3,  // Third round (16 → 8)
  QUARTER_FINAL: 4, // Quarter finals (8 → 4)
  SEMI_FINAL: 5,    // Semi finals (4 → 2)
  FINAL: 6          // Final (2 → 1)
} as const

// Vote Type Constants
export const VOTE_TYPE = {
  TOURNAMENT_VOTE: 'tournament_vote' // Standard tournament voting
} as const

// Tournament Configuration (Legacy - for backward compatibility)
export const TOURNAMENT_CONFIG = {
  // Voting constraints
  MAX_VOTES_PER_USER_PER_MATCHUP: 1, // Users can only vote once per unique matchup

  // Bracket progression (Legacy 64-contestant configuration)
  CONTESTANTS_PER_BRACKET: {
    [BRACKET_ROUNDS.ROUND_OF_64]: 64,
    [BRACKET_ROUNDS.ROUND_OF_32]: 32,
    [BRACKET_ROUNDS.ROUND_OF_16]: 16,
    [BRACKET_ROUNDS.QUARTER_FINAL]: 8,
    [BRACKET_ROUNDS.SEMI_FINAL]: 4,
    [BRACKET_ROUNDS.FINAL]: 2
  },

  // Advancement rules (Legacy 64-contestant configuration)
  CONTESTANTS_TO_ADVANCE_PER_BRACKET: {
    [BRACKET_ROUNDS.ROUND_OF_64]: 32,
    [BRACKET_ROUNDS.ROUND_OF_32]: 16,
    [BRACKET_ROUNDS.ROUND_OF_16]: 8,
    [BRACKET_ROUNDS.QUARTER_FINAL]: 4,
    [BRACKET_ROUNDS.SEMI_FINAL]: 2,
    [BRACKET_ROUNDS.FINAL]: 1
  }
} as const

// Dynamic Tournament Configuration Functions
export const getDynamicTournamentConfig = (maxContestants: number) => {
  const bracketConfig = generateDynamicBracketConfig(maxContestants)

  return {
    MAX_VOTES_PER_USER_PER_MATCHUP: 1,
    CONTESTANTS_PER_BRACKET: bracketConfig.contestantsPerBracket,
    CONTESTANTS_TO_ADVANCE_PER_BRACKET: bracketConfig.contestantsToAdvancePerBracket,
    BRACKET_NAMES: bracketConfig.bracketNames,
    TOTAL_ROUNDS: bracketConfig.totalRounds
  }
}

// Helper functions to get dynamic values
export const getContestantsInBracket = (maxContestants: number, round: number): number => {
  return calculateContestantsInRound(maxContestants, round)
}

export const getContestantsToAdvanceFromBracket = (maxContestants: number, round: number): number => {
  return calculateAdvancingContestants(maxContestants, round)
}

// Dynamic Bracket Calculation Functions
export const calculateTotalRounds = (maxContestants: number): number => {
  // Check if it's a power of 2 first, before calling isValidBracketSize
  if (maxContestants <= 0 || (maxContestants & (maxContestants - 1)) !== 0) {
    throw new Error(`Invalid contestant count: ${maxContestants}. Check supported sizes.`)
  }
  return Math.log2(maxContestants)
}

export const calculateContestantsInRound = (maxContestants: number, round: number): number => {
  const totalRounds = calculateTotalRounds(maxContestants)
  if (round < 1 || round > totalRounds) {
    throw new Error(`Invalid round: ${round}. Must be between 1 and ${totalRounds}`)
  }
  return maxContestants / Math.pow(2, round - 1)
}

export const calculateAdvancingContestants = (maxContestants: number, round: number): number => {
  const totalRounds = calculateTotalRounds(maxContestants)
  if (round >= totalRounds) {
    return 1 // Final round winner
  }
  return calculateContestantsInRound(maxContestants, round) / 2
}

export const getDynamicBracketName = (maxContestants: number, round: number): string => {
  const totalRounds = calculateTotalRounds(maxContestants)
  const contestantsInRound = calculateContestantsInRound(maxContestants, round)

  // Special names for final rounds
  if (round === totalRounds) {
    return 'Final'
  } else if (round === totalRounds - 1) {
    return 'Semi Finals'
  } else if (round === totalRounds - 2 && totalRounds >= 3) {
    return 'Quarter Finals'
  } else {
    return `Round of ${contestantsInRound}`
  }
}

export const generateDynamicBracketConfig = (maxContestants: number) => {
  if (!isValidBracketSize(maxContestants)) {
    throw new Error(`Invalid contestant count: ${maxContestants}. Check supported sizes.`)
  }

  const totalRounds = calculateTotalRounds(maxContestants)
  const contestantsPerBracket: Record<number, number> = {}
  const contestantsToAdvancePerBracket: Record<number, number> = {}
  const bracketNames: Record<number, string> = {}

  for (let round = 1; round <= totalRounds; round++) {
    const contestantsInRound = calculateContestantsInRound(maxContestants, round)
    const advancingContestants = calculateAdvancingContestants(maxContestants, round)

    contestantsPerBracket[round] = contestantsInRound
    contestantsToAdvancePerBracket[round] = advancingContestants
    bracketNames[round] = getDynamicBracketName(maxContestants, round)
  }

  return {
    totalRounds,
    contestantsPerBracket,
    contestantsToAdvancePerBracket,
    bracketNames
  }
}

// Helper functions (updated for dynamic support)
export const getNextBracketRound = (currentRound: number, maxContestants: number): number | null => {
  const totalRounds = calculateTotalRounds(maxContestants)
  if (currentRound >= totalRounds) {
    return null // Tournament is complete
  }
  return currentRound + 1
}

export const getBracketName = (round: number, maxContestants?: number): string => {
  // Backward compatibility: if maxContestants not provided, use legacy hardcoded names
  if (!maxContestants) {
    const names: Record<number, string> = {
      [BRACKET_ROUNDS.ROUND_OF_64]: 'Round of 64',
      [BRACKET_ROUNDS.ROUND_OF_32]: 'Round of 32',
      [BRACKET_ROUNDS.ROUND_OF_16]: 'Round of 16',
      [BRACKET_ROUNDS.QUARTER_FINAL]: 'Quarter Finals',
      [BRACKET_ROUNDS.SEMI_FINAL]: 'Semi Finals',
      [BRACKET_ROUNDS.FINAL]: 'Final'
    }
    return names[round] || `Round ${round}`
  }

  return getDynamicBracketName(maxContestants, round)
}

export const isValidBracketSize = (contestantCount: number, maxLimit?: number): boolean => {
  // Check if contestant count is a valid power of 2
  const isPowerOfTwo = contestantCount > 0 && (contestantCount & (contestantCount - 1)) === 0

  // If no maxLimit provided, just check if it's a power of 2 and within supported sizes
  if (!maxLimit) {
    return isPowerOfTwo && SUPPORTED_TOURNAMENT_SIZES.includes(contestantCount as any)
  }

  // Check if contestant count is a valid power of 2 and within limits
  return isPowerOfTwo && contestantCount <= maxLimit
}

// Additional utility functions
export const getFirstRound = (): number => 1

export const getFinalRound = (maxContestants: number): number => {
  return calculateTotalRounds(maxContestants)
}

export const isFinalRound = (round: number, maxContestants: number): boolean => {
  return round === getFinalRound(maxContestants)
}

export const getTournamentProgress = (currentRound: number, maxContestants: number): {
  currentRound: number
  totalRounds: number
  progressPercentage: number
  roundName: string
} => {
  const totalRounds = calculateTotalRounds(maxContestants)
  const progressPercentage = Math.round((currentRound / totalRounds) * 100)
  const roundName = getDynamicBracketName(maxContestants, currentRound)

  return {
    currentRound,
    totalRounds,
    progressPercentage,
    roundName
  }
}

// Type definitions for better TypeScript support
export type TournamentStatus = typeof TOURNAMENT_STATUS[keyof typeof TOURNAMENT_STATUS]
export type SubmissionStatus = typeof SUBMISSION_STATUS[keyof typeof SUBMISSION_STATUS]
export type BracketRound = typeof BRACKET_ROUNDS[keyof typeof BRACKET_ROUNDS]
export type VoteType = typeof VOTE_TYPE[keyof typeof VOTE_TYPE]
export type SupportedTournamentSize = typeof SUPPORTED_TOURNAMENT_SIZES[number]

// Dynamic bracket configuration type
export interface DynamicBracketConfig {
  totalRounds: number
  contestantsPerBracket: Record<number, number>
  contestantsToAdvancePerBracket: Record<number, number>
  bracketNames: Record<number, string>
}
