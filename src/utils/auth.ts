import * as jwt from 'jsonwebtoken'
import * as bcrypt from 'bcrypt'

// Enhanced security configuration
const JWT_SECRET = process.env.JWT_SECRET
if (!JWT_SECRET) {
  throw new Error('JWT_SECRET environment variable is required')
}
const JWT_ALGORITHM = 'HS256'
const JWT_EXPIRY = '24h'  // Reduced from 7d for better security
const BCRYPT_ROUNDS = 12  // Consider increasing from 10 to 12 for better security

export const hashPassword = (plain: string) => bcrypt.hash(plain, BCRYPT_ROUNDS)
export const comparePassword = (plain: string, hashed: string) => bcrypt.compare(plain, hashed)

export const generateToken = (userId: string) => {
  return jwt.sign({ userId }, JWT_SECRET, { 
    expiresIn: JWT_EXPIRY,
    algorithm: JWT_ALGORITHM
  })
}

export const verifyToken = (token: string) => {
  return jwt.verify(token, JWT_SECRET, {
    algorithms: [JWT_ALGORITHM]
  })
}
