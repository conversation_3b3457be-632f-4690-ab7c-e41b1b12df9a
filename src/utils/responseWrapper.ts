import { Response } from 'express'
import { HttpStatusCode } from '../enums/ErrorEnums'

export interface ApiResponse<T> {
  data: T;
  message: string;
  success: boolean;
  statusCode: number;
}

/**
 * Sanitize error data to prevent information disclosure
 */
const sanitizeErrorData = (data: any, _statusCode: number): any => {
  // In production, never expose internal error details
  if (process.env.NODE_ENV === 'production') {
    return null
  }

  // In development, only expose safe error information
  if (data && typeof data === 'object') {
    // If it's a validation error with structured format, keep it
    if (data.errors && Array.isArray(data.errors)) {
      return {
        errors: data.errors.map((error: any) => ({
          field: error.field || 'unknown',
          message: error.message || 'Validation failed',
          // Remove the actual field value to prevent data exposure
        }))
      }
    }

    // For other errors, only return safe information
    if (data.message && typeof data.message === 'string') {
      return { message: data.message }
    }

    // For database/system errors, return nothing in production-like scenarios
    return null
  }

  return null
}

/**
 * Get sanitized error message based on status code and environment
 */
const getSanitizedErrorMessage = (message: string, statusCode: number): string => {
  // For client errors (4xx), the message is usually safe to return
  if (statusCode >= HttpStatusCode.BAD_REQUEST && statusCode < HttpStatusCode.INTERNAL_SERVER_ERROR) {
    return message
  }

  // For server errors (5xx), use generic messages in production
  if (statusCode >= HttpStatusCode.INTERNAL_SERVER_ERROR) {
    if (process.env.NODE_ENV === 'production') {
      return 'Internal server error'
    }
    return message
  }

  return message
}

export const sendSuccess = <T>(res: Response, data: T, message: string = 'Success', statusCode: number = HttpStatusCode.OK): void => {
  const response: ApiResponse<T> = {
    data,
    message,
    success: true,
    statusCode
  }
  res.status(statusCode).json(response)
}

export const sendError = (res: Response, message: string = 'Error', statusCode: number = HttpStatusCode.INTERNAL_SERVER_ERROR, data: any = null): void => {
  const sanitizedMessage = getSanitizedErrorMessage(message, statusCode)
  const sanitizedData = sanitizeErrorData(data, statusCode)

  const response: ApiResponse<any> = {
    data: sanitizedData,
    message: sanitizedMessage,
    success: false,
    statusCode
  }

  res.status(statusCode).json(response)
}