import { Request } from 'express'

/**
 * Standardized error logging utility
 * Provides consistent error logging format across the application
 */

export interface ErrorLogContext {
  error: string
  stack?: string
  path?: string
  method?: string
  userId?: string
  tournamentId?: string
  timestamp: string
  [key: string]: any
}

/**
 * Log error with standardized format and context
 */
export const logError = (
  operation: string,
  error: any,
  req?: Request,
  additionalContext?: Record<string, any>
): void => {
  const context: ErrorLogContext = {
    error: error instanceof Error ? error.message : 'Unknown error',
    stack: process.env.NODE_ENV === 'development' ? (error instanceof Error ? error.stack : undefined) : undefined,
    timestamp: new Date().toISOString(),
    ...additionalContext
  }

  // Add request context if available
  if (req) {
    context.path = req.path
    context.method = req.method
    context.userId = (req as any).userId
  }

  console.error(`${operation}:`, context)
}

/**
 * Log database operation errors
 */
export const logDatabaseError = (
  operation: string,
  error: any,
  req?: Request,
  entityId?: string
): void => {
  logError(operation, error, req, { entityId })
}

/**
 * Log validation errors
 */
export const logValidationError = (
  operation: string,
  errors: any[],
  req: Request
): void => {
  const context = {
    errorCount: errors.length,
    fields: errors.map(e => e.field || 'unknown'),
    timestamp: new Date().toISOString()
  }

  console.log(`${operation}:`, {
    path: req.path,
    method: req.method,
    ...context
  })
}

/**
 * Log security-related errors
 */
export const logSecurityError = (
  operation: string,
  error: any,
  req: Request,
  additionalContext?: Record<string, any>
): void => {
  const context = {
    securityEvent: true,
    ...additionalContext
  }

  logError(operation, error, req, context)
}

/**
 * Log informational messages with request context
 */
export const logInfo = (
  operation: string,
  message: string,
  req?: Request,
  additionalContext?: Record<string, any>
): void => {
  const context: any = {
    message,
    timestamp: new Date().toISOString(),
    ...additionalContext
  }

  // Add request context if available
  if (req) {
    context.path = req.path
    context.method = req.method
    context.userId = (req as any).userId
  }

  console.log(`${operation}:`, context)
}
