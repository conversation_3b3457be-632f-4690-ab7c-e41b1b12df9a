/**
 * HTTP Status Codes Enum
 * Standard HTTP status codes used throughout the application
 */
export enum HttpStatusCode {
  // Success
  OK = 200,
  CREATED = 201,
  ACCEPTED = 202,
  NO_CONTENT = 204,

  // Client Errors
  BAD_REQUEST = 400,
  UNAUTHORIZED = 401,
  FORBIDDEN = 403,
  NOT_FOUND = 404,
  METHOD_NOT_ALLOWED = 405,
  CONFLICT = 409,
  REQUEST_ENTITY_TOO_LARGE = 413,
  UNPROCESSABLE_ENTITY = 422,
  TOO_MANY_REQUESTS = 429,

  // Server Errors
  INTERNAL_SERVER_ERROR = 500,
  NOT_IMPLEMENTED = 501,
  BAD_GATEWAY = 502,
  SERVICE_UNAVAILABLE = 503,
  GATEWAY_TIMEOUT = 504
}

/**
 * Error Messages Enum
 * Standardized error messages for consistent API responses
 */
export enum ErrorMessage {
  // Generic Messages
  INTERNAL_SERVER_ERROR = 'Internal server error',
  BAD_REQUEST = 'Bad request',
  UNAUTHORIZED = 'Authentication required',
  FORBIDDEN = 'Access denied',
  NOT_FOUND = 'Resource not found',
  CONFLICT = 'Resource conflict',
  TOO_MANY_REQUESTS = 'Too many requests',
  VALIDATION_FAILED = 'Validation failed',

  // Authentication & Authorization
  AUTHENTICATION_FAILED = 'Authentication failed',
  INVALID_TOKEN = 'Invalid authentication token',
  TOKEN_EXPIRED = 'Authentication token expired',
  INSUFFICIENT_PERMISSIONS = 'Insufficient permissions',

  // Database Errors
  DATABASE_ERROR = 'Database operation failed',
  DATABASE_CONNECTION_ERROR = 'Database connection error',
  RECORD_NOT_FOUND = 'Record not found',
  DUPLICATE_ENTRY = 'Duplicate entry',
  FOREIGN_KEY_CONSTRAINT = 'Foreign key constraint violation',

  // Validation Errors
  INVALID_INPUT = 'Invalid input data',
  MISSING_REQUIRED_FIELD = 'Missing required field',
  INVALID_FORMAT = 'Invalid data format',
  INVALID_EMAIL = 'Invalid email format',
  INVALID_PASSWORD = 'Invalid password format',

  // Business Logic Errors
  INSUFFICIENT_FUNDS = 'Insufficient funds',
  TOURNAMENT_FULL = 'Tournament is full',
  TOURNAMENT_NOT_ACTIVE = 'Tournament is not active',
  ALREADY_SUBMITTED = 'Already submitted to this tournament',
  VOTING_NOT_ALLOWED = 'Voting not allowed',

  // System Errors
  SERVICE_UNAVAILABLE = 'Service unavailable',
  EXTERNAL_SERVICE_ERROR = 'External service error',
  RATE_LIMIT_EXCEEDED = 'Rate limit exceeded',
  REQUEST_TIMEOUT = 'Request timeout'
}

/**
 * Error Types Enum
 * Categories of errors for better error handling and logging
 */
export enum ErrorType {
  VALIDATION_ERROR = 'ValidationError',
  AUTHENTICATION_ERROR = 'AuthenticationError',
  AUTHORIZATION_ERROR = 'AuthorizationError',
  NOT_FOUND_ERROR = 'NotFoundError',
  CONFLICT_ERROR = 'ConflictError',
  DATABASE_ERROR = 'DatabaseError',
  EXTERNAL_SERVICE_ERROR = 'ExternalServiceError',
  RATE_LIMIT_ERROR = 'RateLimitError',
  INTERNAL_ERROR = 'InternalError'
}

/**
 * Prisma Error Codes Enum
 * Common Prisma error codes for database operations
 */
export enum PrismaErrorCode {
  UNIQUE_CONSTRAINT_VIOLATION = 'P2002',
  FOREIGN_KEY_CONSTRAINT_VIOLATION = 'P2003',
  RECORD_NOT_FOUND = 'P2025',
  INVALID_DATA = 'P2006',
  CONNECTION_ERROR = 'P1001',
  DATABASE_NOT_FOUND = 'P1003',
  TIMEOUT = 'P1008'
}

/**
 * Error Severity Enum
 * Used for logging and monitoring purposes
 */
export enum ErrorSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

/**
 * Error mapping for Prisma errors to HTTP responses
 */
export const PRISMA_ERROR_MAP: Record<string, { status: HttpStatusCode; message: ErrorMessage }> = {
  [PrismaErrorCode.UNIQUE_CONSTRAINT_VIOLATION]: {
    status: HttpStatusCode.CONFLICT,
    message: ErrorMessage.DUPLICATE_ENTRY
  },
  [PrismaErrorCode.FOREIGN_KEY_CONSTRAINT_VIOLATION]: {
    status: HttpStatusCode.BAD_REQUEST,
    message: ErrorMessage.FOREIGN_KEY_CONSTRAINT
  },
  [PrismaErrorCode.RECORD_NOT_FOUND]: {
    status: HttpStatusCode.NOT_FOUND,
    message: ErrorMessage.RECORD_NOT_FOUND
  },
  [PrismaErrorCode.CONNECTION_ERROR]: {
    status: HttpStatusCode.SERVICE_UNAVAILABLE,
    message: ErrorMessage.DATABASE_CONNECTION_ERROR
  },
  [PrismaErrorCode.TIMEOUT]: {
    status: HttpStatusCode.GATEWAY_TIMEOUT,
    message: ErrorMessage.REQUEST_TIMEOUT
  }
}
