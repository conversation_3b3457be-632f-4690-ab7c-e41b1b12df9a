/**
 * GetRankt Backend API Application Entry Point
 * 
 * Clean, focused entry point that initializes and starts the application server.
 * All configuration and setup logic has been moved to dedicated classes.
 */

import dotenv from 'dotenv'
import { AppServer } from './server/AppServer'

// Load environment variables first
dotenv.config()

/**
 * Application startup function
 */
async function startApplication(): Promise<void> {
    const appServer = new AppServer()
    await appServer.start()
}

// Start the application
startApplication().catch((error) => {
  console.error('💥 Failed to startup application:', error)
  process.exit(1)
})
