import * as express from 'express'
import { purchaseCoins, getCoinTransactionHistory } from '../controllers/coinController'
import { requireAuth } from '../middlewares/authMiddleware'

const router = express.Router()

// Coin purchase route
router.post('/purchase', requireAuth, purchaseCoins)

// Get coin transaction history
router.get('/transactions', requireAuth, getCoinTransactionHistory)

export default router
